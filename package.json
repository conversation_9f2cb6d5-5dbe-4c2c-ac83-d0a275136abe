{"name": "wise-winn-salary-for-life", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/themes": "^3.2.0", "@tanstack/react-table": "^8.20.6", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "framer-motion": "^12.12.1", "lucide-react": "^0.487.0", "next": "14.2.3", "next-auth": "^4.24.11", "radix-ui": "^1.1.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-colorful": "^5.6.1", "react-csv": "^2.2.2", "react-day-picker": "^8.7.1", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-hook-form": "^7.43.9", "react-hot-toast": "^2.4.1", "react-image-crop": "^11.0.6", "react-infinite-scroll-hook": "^4.1.1", "react-mentions": "^4.4.10", "react-minimal-pie-chart": "^8.4.0", "react-moment": "^1.1.3", "react-number-format": "^5.3.1", "react-paystack": "^4.0.3", "react-pdf": "^7.7.3", "react-phone-input-2": "^2.15.1", "react-pin-input": "^1.3.1", "react-qr-code": "^2.0.15", "react-query": "^3.39.3", "react-responsive": "^10.0.0", "react-select": "^5.8.0", "react-share": "^5.1.1", "react-slick": "^0.30.2", "react-text-transition": "^3.1.0", "react-to-print": "^2.15.1", "react-use": "^17.5.0", "react-webcam": "^7.2.0", "react-wrap-balancer": "^0.5.0", "recharts": "^2.15.0", "stream-chat-react": "^12.10.0", "tailwind-merge": "^2.6.0", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}