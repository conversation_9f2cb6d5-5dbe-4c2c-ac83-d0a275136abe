// "use client"

// import { Button, Input } from "@/components/core"
// import { ChevronRight, Plus, Minus } from "lucide-react"
// import Link from "next/link"
// import { useState } from "react"

// export default function HomePage() {
//   const [ticketCount, setTicketCount] = useState(1)

//   const incrementTickets = () => {
//     setTicketCount(prev => prev + 1)
//   }

//   const decrementTickets = () => {
//     setTicketCount(prev => Math.max(1, prev - 1))
//   }

//   return (
//     <div className="min-h-screen relative overflow-hidden px-4 sm:px-8 md:px-16 lg:px-[92px] py-4 sm:py-8 md:py-[53px]">
//       {/* Background image - full coverage */}
//       <div
//         className="absolute bg-center inset-0 bg-no-repeat"
//         style={{
//           backgroundImage: "url('/background.png')",
//           backgroundSize: "cover",
//           width: "100%",
//           height: "100%",
//         }}
//       ></div>

//       {/* Header */}
//       <header className="relative z-10 flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-0">
//         <Link href="/" className="text-white text-xl sm:text-2xl font-bold font-clash">
//           Salary 4 Life
//         </Link>

//         <nav className=" items-center space-x-4 sm:space-x-8 text-[#9C9C9C] text-xs sm:text-[13px] order-3 sm:order-2 hidden">
//           <Link href="/" className="hover:text-white transition-colors">
//             Home
//           </Link>
//           <Link href="/partner" className="hover:text-white transition-colors">
//             Partner
//           </Link>
//           <Link href="/faqs" className="hover:text-white transition-colors">
//             FAQs
//           </Link>
//           <Link href="/community" className="hover:text-white transition-colors whitespace-nowrap">
//             Join Our Community
//           </Link>
//         </nav>

//         <div className="flex items-center space-x-2 sm:space-x-3 order-2 sm:order-3">
//           <Link href="/login">
//             <Button
//               variant="default"
//               className="border-[#FFFFFF] text-white text-xs sm:text-sm bg-[#FFFFFF33] py-2 sm:py-[10px] px-4 sm:px-8"
//             >
//               Log in
//             </Button>
//           </Link>
//           <Link href="/register">
//             <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-xs sm:text-sm py-2 sm:py-[10px] px-4 sm:px-8">
//               Register
//             </Button>
//           </Link>
//         </div>
//       </header>

//       {/* Main Content */}
//       <main className="relative z-10 flex items-center justify-center min-h-[calc(100vh-120px)] sm:min-h-[calc(100vh-80px)] px-4">
//         {/* Card with glowing border effect */}
//         <div className="relative w-full max-w-[519px]">
//           {/* Glow effect */}
//           <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-lg blur-sm opacity-75 animate-pulse"></div>

//           <div className="relative py-6 sm:py-[38px] px-4 sm:px-8 w-full bg-[#000410] backdrop-blur-sm border border-gray-800/50 shadow-xl rounded-[10px]">
//             <div className="text-center pb-4 sm:pb-6 flex flex-col items-center">
//               <h1 className="text-2xl sm:text-3xl font-semibold text-white mb-2 sm:mb-3 font-clash">Register to play</h1>
//               <p className="text-[#818181] text-xs leading-[20px] max-w-[313px] px-2 sm:px-0">
//                 Enter your details below and select th number of tickets you wish to purchase to take part in the Salary
//                 4 Life game show
//               </p>
//             </div>

//             <div className="space-y-4 sm:space-y-6">
//               <div>
//                 <label className="text-white font-medium mb-3 sm:mb-4 block text-xs sm:text-[13px]">How many tickets do you want to buy?</label>
//                 <div className="flex items-center gap-2 sm:gap-3">
//                   <Button
//                     type="button"
//                     onClick={decrementTickets}
//                     className="w-8 h-8 sm:w-[37px] sm:h-[37px] bg-[#121212] border border-gray-700 text-white hover:bg-gray-800 flex items-center justify-center rounded-[8px] flex-shrink-0"
//                   >
//                     <Minus className="w-3 h-3 sm:w-4 sm:h-4" />
//                   </Button>
//                   <div className="flex-1 bg-[#121212] border border-gray-700 text-[#FFFFFF66] h-8 sm:h-[38px] text-xs sm:text-[13px] px-3 sm:px-4 py-2 rounded-[8px] text-left flex items-center">
//                     {ticketCount} ticket(s)
//                   </div>
//                   <Button
//                     type="button"
//                     onClick={incrementTickets}
//                     className="w-8 h-8 sm:w-[37px] sm:h-[37px] bg-[#121212] border border-gray-700 text-white hover:bg-gray-800 flex items-center justify-center rounded-[8px] flex-shrink-0"
//                   >
//                     <Plus className="w-3 h-3 sm:w-4 sm:h-4" />
//                   </Button>
//                 </div>
//               </div>

//               <div>
//                 <label className="text-white font-medium mb-2 sm:mb-3 block text-xs sm:text-[13px]">Enter your phone number</label>
//                 <Input
//                   placeholder="Enter your phone number"
//                   className="bg-[#121212] border-gray-700 !text-xs sm:!text-[13px] text-white placeholder-gray-500 focus:border-purple-500 focus:ring-purple-500/20 h-10 sm:h-auto"
//                 />
//               </div>

// <div className="pt-3 sm:pt-4">
//   <div className="flex items-center justify-center space-x-2 mb-6 sm:mb-[45px]">
//     <svg width="80" height="1" viewBox="0 0 127 1" fill="none" xmlns="http://www.w3.org/2000/svg" className="sm:w-[127px]">
//       <line x1="1.31134e-08" y1="0.85" x2="127" y2="0.850011" stroke="#CACACA" stroke-width="0.3" />
//     </svg>

//     <p className="text-gray-400 text-center text-xs whitespace-nowrap px-2">Continue payment with</p>

//     <svg width="80" height="1" viewBox="0 0 127 1" fill="none" xmlns="http://www.w3.org/2000/svg" className="sm:w-[127px]">
//       <line x1="1.31134e-08" y1="0.85" x2="127" y2="0.850011" stroke="#CACACA" stroke-width="0.3" />
//     </svg>

//   </div>

//                 <div className="space-y-3 sm:space-y-6">
//                   <Button
//                     variant="default"
//                     className="w-full bg-[#F9FAFB] text-black justify-between h-10 sm:h-12"
//                   >
//                     <div className="flex items-center gap-2 sm:gap-[14px]">
//                       <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="sm:w-5 sm:h-5">
//                         <path d="M6.66667 18.3334H13.3333C15.6333 18.3334 17.5 16.4667 17.5 14.1667V5.83335C17.5 3.53335 15.6333 1.66669 13.3333 1.66669H6.66667C4.36667 1.66669 2.5 3.53335 2.5 5.83335V14.1667C2.5 16.4667 4.36667 18.3334 6.66667 18.3334Z" fill="#4C1961" />
//                         <path d="M12.5 4.75836H7.50002C6.64168 4.75836 5.93335 5.45836 5.93335 6.32503V7.15836C5.93335 8.0167 6.63335 8.72503 7.50002 8.72503H12.5C13.3583 8.72503 14.0667 8.02503 14.0667 7.15836V6.32503C14.0667 5.45836 13.3667 4.75836 12.5 4.75836Z" fill="white" />
//                         <path d="M6.79992 12.4333C6.68325 12.4333 6.57492 12.4083 6.47492 12.3667C6.37492 12.325 6.28325 12.2667 6.20825 12.1917C6.04992 12.0333 5.95825 11.825 5.95825 11.6C5.95825 11.4917 5.98325 11.3833 6.02492 11.2833C6.06659 11.175 6.12492 11.0917 6.20825 11.0083C6.39992 10.8167 6.69159 10.725 6.95825 10.7833C7.00825 10.7917 7.06659 10.8083 7.11659 10.8333C7.16659 10.85 7.21659 10.875 7.25825 10.9083C7.30825 10.9333 7.34992 10.975 7.38325 11.0083C7.45825 11.0917 7.52492 11.175 7.56659 11.2833C7.60825 11.3833 7.62492 11.4917 7.62492 11.6C7.62492 11.825 7.54159 12.0333 7.38325 12.1917C7.22492 12.35 7.01659 12.4333 6.79992 12.4333Z" fill="white" />
//                         <path d="M10.1251 12.4333C9.90841 12.4333 9.70008 12.35 9.54175 12.1917C9.38341 12.0333 9.29175 11.825 9.29175 11.6C9.29175 11.3833 9.38341 11.1667 9.54175 11.0083C9.85008 10.7 10.4084 10.7 10.7167 11.0083C10.7917 11.0917 10.8584 11.175 10.9001 11.2833C10.9417 11.3833 10.9584 11.4917 10.9584 11.6C10.9584 11.825 10.8751 12.0333 10.7167 12.1917C10.5584 12.35 10.3501 12.4333 10.1251 12.4333Z" fill="white" />
//                         <path d="M13.4583 12.4333C13.2417 12.4333 13.0333 12.35 12.875 12.1917C12.7167 12.0333 12.625 11.825 12.625 11.6C12.625 11.3833 12.7167 11.1667 12.875 11.0083C13.1833 10.7 13.7417 10.7 14.05 11.0083C14.2083 11.1667 14.3 11.3833 14.3 11.6C14.3 11.7083 14.275 11.8167 14.2333 11.9167C14.1917 12.0167 14.1333 12.1083 14.05 12.1917C13.8917 12.35 13.6833 12.4333 13.4583 12.4333Z" fill="white" />
//                         <path d="M6.79992 15.7667C6.57492 15.7667 6.36659 15.6834 6.20825 15.525C6.04992 15.3667 5.95825 15.1584 5.95825 14.9334C5.95825 14.7167 6.04992 14.5 6.20825 14.3417C6.28325 14.2667 6.37492 14.2084 6.47492 14.1667C6.68325 14.0834 6.90825 14.0834 7.11659 14.1667C7.16659 14.1834 7.21659 14.2084 7.25825 14.2417C7.30825 14.2667 7.34992 14.3084 7.38325 14.3417C7.54159 14.5 7.63325 14.7167 7.63325 14.9334C7.63325 15.1584 7.54159 15.3667 7.38325 15.525C7.22492 15.6834 7.01659 15.7667 6.79992 15.7667Z" fill="white" />
//                         <path d="M10.1251 15.7667C9.90841 15.7667 9.70008 15.6833 9.54175 15.525C9.38341 15.3667 9.29175 15.1583 9.29175 14.9333C9.29175 14.875 9.30008 14.825 9.30842 14.7667C9.32508 14.7167 9.34175 14.6667 9.35841 14.6167C9.38341 14.5667 9.40841 14.5167 9.43341 14.4667C9.46675 14.425 9.50008 14.3833 9.54175 14.3417C9.61675 14.2667 9.70842 14.2083 9.80842 14.1667C10.1167 14.0417 10.4834 14.1083 10.7167 14.3417C10.8751 14.5 10.9584 14.7167 10.9584 14.9333C10.9584 15.1583 10.8751 15.3667 10.7167 15.525C10.6417 15.6 10.5501 15.6583 10.4501 15.7C10.3501 15.7417 10.2417 15.7667 10.1251 15.7667Z" fill="white" />
//                         <path d="M13.4583 15.7667C13.35 15.7667 13.2416 15.7417 13.1416 15.7C13.0416 15.6583 12.95 15.6 12.875 15.525C12.7166 15.3667 12.6333 15.1583 12.6333 14.9333C12.6333 14.7167 12.7166 14.5 12.875 14.3417C13.1 14.1083 13.475 14.0417 13.7833 14.1667C13.8833 14.2083 13.975 14.2667 14.05 14.3417C14.2083 14.5 14.2916 14.7167 14.2916 14.9333C14.2916 15.1583 14.2083 15.3667 14.05 15.525C13.8916 15.6833 13.6833 15.7667 13.4583 15.7667Z" fill="white" />
//                       </svg>
//                       <span className="font-bold text-xs sm:text-sm font-montserrat">USSD</span>
//                     </div>
//                     <svg width="14" height="14" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="sm:w-[18px] sm:h-[18px]">
//                       <path d="M6.75004 15.5025C6.89254 15.5025 7.03504 15.45 7.14754 15.3375L12.0375 10.4475C12.8325 9.65252 12.8325 8.34751 12.0375 7.55251L7.14754 2.66251C6.93004 2.44501 6.57004 2.44501 6.35254 2.66251C6.13504 2.88001 6.13504 3.24001 6.35254 3.45751L11.2425 8.34751C11.6025 8.70751 11.6025 9.29251 11.2425 9.65251L6.35254 14.5425C6.13504 14.76 6.13504 15.12 6.35254 15.3375C6.46504 15.4425 6.60754 15.5025 6.75004 15.5025Z" fill="black" />
//                     </svg>

//                   </Button>

//                   <Button
//                     variant="default"
//                     className="w-full bg-black hover:bg-gray-900 border-[0.2px] border-[#C4C4C4] text-white justify-between h-12"
//                   >
//                     <div className="flex items-center gap-[14px]">
//                       <svg
//                         width={30}
//                         height={30}
//                         viewBox="0 0 30 30"
//                         fill="none"
//                         xmlns="http://www.w3.org/2000/svg"
//                         xmlnsXlink="http://www.w3.org/1999/xlink"
//                       >
//                         <circle cx={15} cy={15} r={15} fill="white" />
//                         <rect
//                           x={8}
//                           y={8}
//                           width={15.2568}
//                           height={15}
//                           fill="url(#pattern0_16229_34289)"
//                         />
//                         <defs>
//                           <pattern
//                             id="pattern0_16229_34289"
//                             patternContentUnits="objectBoundingBox"
//                             width={1}
//                             height={1}
//                           >
//                             <use
//                               xlinkHref="#image0_16229_34289"
//                               transform="matrix(0.003367 0 0 0.00342466 -0.363636 -0.376712)"
//                             />
//                           </pattern>
//                           <image
//                             id="image0_16229_34289"
//                             width={500}
//                             height={500}
//                             preserveAspectRatio="none"
//                             xlinkHref="data:image/png;base64,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"
//                           />
//                         </defs>
//                       </svg>
//                       <span className="font-bold text-sm font-montserrat">paystack</span>
//                     </div>
//                     <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
//                       <path d="M6.75004 15.5025C6.89254 15.5025 7.03504 15.45 7.14754 15.3375L12.0375 10.4475C12.8325 9.65252 12.8325 8.34751 12.0375 7.55251L7.14754 2.66251C6.93004 2.44501 6.57004 2.44501 6.35254 2.66251C6.13504 2.88001 6.13504 3.24001 6.35254 3.45751L11.2425 8.34751C11.6025 8.70751 11.6025 9.29251 11.2425 9.65251L6.35254 14.5425C6.13504 14.76 6.13504 15.12 6.35254 15.3375C6.46504 15.4425 6.60754 15.5025 6.75004 15.5025Z" fill="white" />
//                     </svg>


//                   </Button>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </main>
//     </div>
//   )
// }


"use client"



import { ChevronRight } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button, Input } from "@/components/core"



export default function HomePage() {
  const [phoneNumber, setPhoneNumber] = useState("")
  const [selectedTickets, setSelectedTickets] = useState<string[]>([])
  const router = useRouter()

  const tickets = [
    { id: "AD-2321942", price: 1000 },
    { id: "AD-2321943", price: 1000 },
    { id: "**********", price: 1000 },
    { id: "**********", price: 1000 },
    { id: "**********", price: 1000 },
    { id: "**********", price: 1000 },
    { id: "**********", price: 1000 },
    { id: "**********", price: 1000 },
  ]

  const toggleTicket = (ticketId: string) => {
    setSelectedTickets((prev) => (prev.includes(ticketId) ? prev.filter((id) => id !== ticketId) : [...prev, ticketId]))
  }

  const totalAmount = selectedTickets.length * 1000
  const ticketCount = selectedTickets.length

  const handlePayment = () => {
    if (selectedTickets.length === 0 || !phoneNumber) {
      alert("Please select tickets and enter your phone number")
      return
    }

    // Generate a random ticket number for the thank you page
    const ticketNumber = `S4L${Math.floor(Math.random() * 900000 + 100000)}`

    // Navigate to thank you page
    router.push(
      `/thank-you?name=Player&ticket=${ticketNumber}&count=${ticketCount}&phone=${encodeURIComponent(phoneNumber)}`,
    )
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background with lottery header */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/background.png')",
          backgroundSize: "cover",
        }}
      ></div>

      {/* Lottery Header Banner */}
      <div
        className="relative h-[200px] sm:h-[250px] md:h-[314px] bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/lottery-header.png')",
          backgroundSize: "cover",
        }}
      >
        {/* Header Navigation */}
        <header className="relative z-10 flex flex-col sm:flex-row items-center justify-between px-4 sm:px-8 lg:px-[92px] py-4 sm:py-[20px] bg-[#000000CC] gap-4 sm:gap-0">
          <Link href="/" className="text-white text-xl sm:text-2xl font-bold font-clash">
            S4L Golden Hour
          </Link>

          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-8 items-center w-full sm:w-auto">
            <nav className="flex items-center space-x-4 sm:space-x-8 text-white text-sm order-2 sm:order-1">
              <Link href="/buy-tickets/winners" className="hover:text-yellow-300 transition-colors">
                Winners
              </Link>
              <Link href="/buy-tickets/results" className="hover:text-yellow-300 transition-colors">
                Results
              </Link>
            </nav>

            <Link href="/buy-tickets/cashout" className="order-1 sm:order-2">
              <Button className="bg-white text-[#4C1961] px-6 sm:px-12 py-2 sm:py-3 rounded-lg font-bold text-sm">
                Cashout
              </Button>
            </Link>
          </div>
        </header>

      </div>

      {/* Main Content */}
      <main className="relative z-10 flex items-start justify-center pt-4 sm:pt-8 px-4 -mt-16 sm:-mt-44">
        {/* Ticket Purchase Card */}
        <div className="relative w-full max-w-[519px]">
          {/* Glow effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-lg blur-sm opacity-75"></div>

          <div className="relative py-6 sm:py-8 px-4 sm:px-8 w-full bg-[#000410] backdrop-blur-sm border border-gray-800/50 shadow-xl rounded-[15px]">
            {/* Header */}
            <div className="text-center mb-4 sm:mb-6">
              <div className="flex item-center justify-center">
                <div className="text-green-400 text-xs sm:text-sm font-semibold mb-2 bg-[#01AE531A] py-[6px] px-[16px] sm:px-[22px] max-w-[140px] rounded-lg">Rootfm9jah</div>
              </div>
              <h2 className="text-xl sm:text-2xl font-bold text-white mb-2 font-clash">Buy Golden hour ticket</h2>
              <div className="flex items-center justify-center px-2">
                <p className="text-[#818181] text-xs sm:text-[13px] max-w-[313px]">
                  Enter phone number and select tickets to purchase and{" "}
                  <span className="text-white font-semibold">win big!</span>
                </p>
              </div>
            </div>

            {/* Phone Number Input */}
            <div className="mb-4 sm:mb-6">
              <label className="text-white font-medium mb-2 sm:mb-3 block text-xs sm:text-sm">Enter your phone number</label>
              <Input
                placeholder="Phone number"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                className="bg-[#1A1B2E] border-gray-600 text-white placeholder-gray-500 focus:border-purple-500 focus:ring-purple-500/20 h-10 sm:h-12 text-sm"
              />
            </div>

            {/* Ticket Selection */}
            <div className="mb-4 sm:mb-6">
              <label className="text-[#FFFFFF80] font-medium mb-3 sm:mb-4 block text-xs sm:text-sm">Select tickets</label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                {tickets.map((ticket) => (
                  <div
                    key={ticket.id}
                    onClick={() => toggleTicket(ticket.id)}
                    className={`flex items-center justify-between p-2 sm:p-3 rounded-lg border cursor-pointer transition-all ${selectedTickets.includes(ticket.id)
                      ? "bg-purple-600/20 border-purple-500"
                      : "bg-[#1A1B2E] border-gray-600 hover:border-gray-500"
                      }`}
                  >
                    <div className="flex items-center gap-2 sm:gap-3">
                      <div
                        className={`w-3 h-3 sm:w-4 sm:h-4 rounded-full border-2 ${selectedTickets.includes(ticket.id) ? "bg-purple-500 border-purple-500" : "border-gray-400"
                          }`}
                      ></div>
                      <span className="text-white text-xs sm:text-[13px] font-medium">{ticket.id}</span>
                    </div>
                    <div className="bg-[#4C1961] text-white px-2 sm:px-3 py-1 rounded text-xs font-semibold">
                      ₦{ticket.price.toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Summary */}
            <div className="mb-4 sm:mb-6 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400 text-xs">Tickets count</span>
                <span className="text-white font-semibold text-xs sm:text-sm">₦{ticketCount.toLocaleString()}.00</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400 text-xs">Purchase amount</span>
                <span className="text-white font-semibold text-xs sm:text-sm">₦{totalAmount.toLocaleString()}.00</span>
              </div>
            </div>

            {/* Payment Section */}
            <div className="text-center">
              <div className="pt-2 sm:pt-4">
                <div className="flex items-center justify-center space-x-2 mb-4 sm:mb-6">
                  <svg width="60" height="1" viewBox="0 0 127 1" fill="none" xmlns="http://www.w3.org/2000/svg" className="sm:w-[127px]">
                    <line x1="1.31134e-08" y1="0.85" x2="127" y2="0.850011" stroke="#CACACA" strokeWidth="0.3" />
                  </svg>

                  <p className="text-gray-400 text-center text-xs whitespace-nowrap px-2">Continue payment with</p>

                  <svg width="60" height="1" viewBox="0 0 127 1" fill="none" xmlns="http://www.w3.org/2000/svg" className="sm:w-[127px]">
                    <line x1="1.31134e-08" y1="0.85" x2="127" y2="0.850011" stroke="#CACACA" strokeWidth="0.3" />
                  </svg>

                </div>
              </div>

              {/* <Button
                onClick={handlePayment}
                className="w-full bg-white hover:bg-gray-100 text-black justify-between h-12 rounded-lg"
                disabled={selectedTickets.length === 0 || !phoneNumber}
              >
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-full"></div>
                  </div>
                  <span className="font-bold text-sm">paystack</span>
                </div>
                <ChevronRight className="w-4 h-4" />
              </Button> */}

              <Button
                onClick={handlePayment}
                variant="default"
                className="w-full bg-white hover:bg-gray-900 border-[0.2px] border-[#C4C4C4] text-black justify-between h-10 sm:h-12"
                disabled={selectedTickets.length === 0 || !phoneNumber}
              >
                <div className="flex items-center gap-2 sm:gap-[14px]">
                  <svg
                    width={24}
                    height={24}
                    viewBox="0 0 30 30"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlnsXlink="http://www.w3.org/1999/xlink"
                    className="sm:w-[30px] sm:h-[30px]"
                  >
                    <circle cx={15} cy={15} r={15} fill="black" />
                    <rect
                      x={8}
                      y={8}
                      width={15.2568}
                      height={15}
                      fill="url(#pattern0_16262_21620)"
                    />
                    <defs>
                      <pattern
                        id="pattern0_16262_21620"
                        patternContentUnits="objectBoundingBox"
                        width={1}
                        height={1}
                      >
                        <use
                          xlinkHref="#image0_16262_21620"
                          transform="matrix(0.003367 0 0 0.00342466 -0.363636 -0.376712)"
                        />
                      </pattern>
                      <image
                        id="image0_16262_21620"
                        width={500}
                        height={500}
                        preserveAspectRatio="none"
                        xlinkHref="data:image/png;base64,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"
                      />
                    </defs>
                  </svg>
                  <span className="font-bold text-xs sm:text-sm font-montserrat text-[#000000]">paystack</span>
                </div>
                <svg width="16" height="16" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="sm:w-[18px] sm:h-[18px]">
                  <path d="M6.75004 15.5025C6.89254 15.5025 7.03504 15.45 7.14754 15.3375L12.0375 10.4475C12.8325 9.65252 12.8325 8.34751 12.0375 7.55251L7.14754 2.66251C6.93004 2.44501 6.57004 2.44501 6.35254 2.66251C6.13504 2.88001 6.13504 3.24001 6.35254 3.45751L11.2425 8.34751C11.6025 8.70751 11.6025 9.29251 11.2425 9.65251L6.35254 14.5425C6.13504 14.76 6.13504 15.12 6.35254 15.3375C6.46504 15.4425 6.60754 15.5025 6.75004 15.5025Z" fill="black" />
                </svg>


              </Button>

            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

