"use client"

import { ChevronRight, Search } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button, Input } from "@/components/core"



export default function HomePage() {
  const [searchTerm, setSearchTerm] = useState("")

  const resultsData = [
    {
      date: "7/21/2022",
      time: "20:44:02",
      winners: ["SL-23219", "SL-23219", "SL-23219", "SL-23219", "SL-23219", "SL-23219", "SL-23219", "SL-23219"],
    },
    {
      date: "8/15/2022",
      time: "14:30:45",
      winners: ["SL-234A2K", "SL-234A2K", "SL-234A2K", "SL-234A2K", "SL-234A2K", "SL-234A2K", "SL-234A2K", "SL-234A2K"],
    },
    {
      date: "9/01/2022",
      time: "10:15:20",
      winners: ["SL-235B6M", "SL-235B6M", "SL-235B6M", "SL-235B6M", "SL-235B6M", "SL-235B6M", "SL-235B6M", "SL-235B6M"],
    },
  ]

  const filteredResults = resultsData.filter((result) =>
    result.winners.some((winner) => winner.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background with lottery header */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/background.png')",
          backgroundSize: "cover",
        }}
      ></div>

      {/* Lottery Header Banner */}
      <div
        className="relative h-[200px] sm:h-[250px] md:h-[314px] bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/lottery-header.png')",
          backgroundSize: "cover",
        }}
      >
        {/* Header Navigation */}
        <header className="relative z-10 flex flex-col sm:flex-row items-center justify-between px-4 sm:px-8 lg:px-[92px] py-4 sm:py-[20px] bg-[#000000CC] gap-4 sm:gap-0">
          <Link href="/" className="text-white text-xl sm:text-2xl font-bold font-clash">
            S4L Golden Hour
          </Link>

          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-8 items-center w-full sm:w-auto">
            <nav className="flex items-center space-x-4 sm:space-x-8 text-white text-sm order-2 sm:order-1">
              <Link href="/buy-tickets/winners" className="hover:text-yellow-300 transition-colors text-[#818181]">
                Winners
              </Link>
              <Link href="/buy-tickets/results" className="hover:text-yellow-300 transition-colors">
                Results
              </Link>
            </nav>

            <Link href="/buy-tickets/cashout" className="order-1 sm:order-2">
              <Button className="bg-white text-[#4C1961] px-6 sm:px-12 py-2 sm:py-3 rounded-lg font-bold text-sm">
                Cashout
              </Button>
            </Link>
          </div>
        </header>

      </div>

      {/* Main Content */}
      <main className="relative z-10 flex items-start justify-center pt-4 sm:pt-8 px-4 -mt-16 sm:-mt-44">
        {/* Ticket Purchase Card */}
        <div className="relative w-full max-w-[796px]">
          {/* Glow effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-lg blur-sm opacity-75"></div>

          <div className="relative py-6 sm:py-[38px] px-4 sm:px-8 w-full bg-[#000410] backdrop-blur-sm border border-gray-800/50 shadow-xl rounded-[10px]">
            {/* Page Header */}
            <div className="text-center mb-[18px]">
              <h1 className="text-4xl font-bold text-white mb-3 font-clash">.Results.</h1>
              <p className="text-[#818181] text-[13px] max-w-[387px] mx-auto">
                Here's where the magic happens! Check below to see if your ticket made it to our list of lucky winners.
              </p>
            </div>

            {/* Search Bar */}
            <div className="max-w-md mx-auto mb-8">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="Search your ticket"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="bg-[#1A1B2E] border-gray-600 text-white placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500/20 h-[45px] pl-12 pr-4 rounded-lg"
                />
              </div>
            </div>

            {/* Results Cards */}
            <div className="max-w-4xl mx-auto space-y-8">
              {(searchTerm ? filteredResults : resultsData).map((result, index) => (
                <div key={index} className="relative">
                  {/* Glow effect */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-lg blur-sm opacity-30"></div>

                  <div className="relative bg-[#0A0B1E] backdrop-blur-sm border border-gray-800/50 shadow-xl rounded-[15px] p-8">
                    {/* Card Header */}
                    <div className="flex items-center flex-col md:flex-row mb-3 gap-4">
                      <h3 className="text-base font-bold text-white">Golden Hour Winners</h3>
                      <div className="text-white text-sm">
                        <span className="mr-4 "><span className="text-xs text-[#A6A6A6]">Date:</span> {result.date}</span>
                        <span className="text-white">{result.time}</span>
                      </div>
                    </div>

                    {/* Winning Numbers Grid */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {result.winners.map((winner, winnerIndex) => (
                        <div
                          key={winnerIndex}
                          className={`bg-[#2A2B3E] border border-gray-600 rounded-[5px] p-2 text-center transition-all ${searchTerm && winner.toLowerCase().includes(searchTerm.toLowerCase())
                            ? "bg-purple-600/20 border-purple-500 ring-2 ring-purple-500/50"
                            : ""
                            }`}
                        >
                          <span className="text-white font-medium text-[13px]">{winner}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}

              {/* No Results Message */}
              {searchTerm && filteredResults.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-lg mb-4">No results found for "{searchTerm}"</div>
                  <p className="text-gray-500">Try searching with a different ticket number.</p>
                </div>
              )}

              {/* Load More Button */}
              <div className="text-center pt-8">
                <Button
                  variant="outlined"
                  className="bg-transparent border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white px-8 py-3"
                >
                  Load More Results
                </Button>
              </div>
            </div>

          </div>
        </div>
      </main>
    </div>
  )
}

