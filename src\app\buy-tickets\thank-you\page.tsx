"use client"

import { Button, Input } from "@/components/core"
import { ChevronRight, Plus, Minus } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

export default function HomePage() {
  const [ticketCount, setTicketCount] = useState(1)

  const incrementTickets = () => {
    setTicketCount(prev => prev + 1)
  }

  const decrementTickets = () => {
    setTicketCount(prev => Math.max(1, prev - 1))
  }

  return (
    <div className="min-h-screen relative overflow-hidden px-4 sm:px-8 md:px-16 lg:px-[92px] py-4 sm:py-8 md:py-[53px]">
      {/* Background image - full coverage */}
      <div
        className="absolute bg-center inset-0 bg-no-repeat"
        style={{
          backgroundImage: "url('/background.png')",
          backgroundSize: "cover",
          width: "100%",
          height: "100%",
        }}
      ></div>

      {/* Header */}
      <header className="relative z-10 flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-0">
        <Link href="/" className="text-white text-xl sm:text-2xl font-bold font-clash">
          Salary 4 Life
        </Link>

        <nav className=" items-center space-x-4 sm:space-x-8 text-[#9C9C9C] text-xs sm:text-[13px] order-3 sm:order-2 hidden">
          <Link href="/" className="hover:text-white transition-colors">
            Home
          </Link>
          <Link href="/partner" className="hover:text-white transition-colors">
            Partner
          </Link>
          <Link href="/faqs" className="hover:text-white transition-colors">
            FAQs
          </Link>
          <Link href="/community" className="hover:text-white transition-colors whitespace-nowrap">
            Join Our Community
          </Link>
        </nav>

        <div className="flex items-center space-x-2 sm:space-x-3 order-2 sm:order-3">
          <Link href="/login">
            <Button
              variant="default"
              className="border-[#FFFFFF] text-white text-xs sm:text-sm bg-[#FFFFFF33] py-2 sm:py-[10px] px-4 sm:px-8"
            >
              Log in
            </Button>
          </Link>
          <Link href="/register">
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-xs sm:text-sm py-2 sm:py-[10px] px-4 sm:px-8">
              Register
            </Button>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex items-center justify-center min-h-[calc(100vh-120px)] sm:min-h-[calc(100vh-80px)] px-4">
        {/* Card with glowing border effect */}
        <div className="relative w-full max-w-[447px]">
          {/* Glow effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-lg blur-sm opacity-75 animate-pulse"></div>

          <div className="relative py-6 sm:py-[38px] px-4 sm:px-8 w-full bg-[#000410] backdrop-blur-sm border border-gray-800/50 shadow-xl rounded-[10px]">
            <div className="text-center pb-4 sm:pb-6 flex flex-col items-center">
              <svg width="118" height="114" viewBox="0 0 118 114" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="59.1649" cy="61.2648" r="45.6605" fill="#01AE53" fill-opacity="0.2" />
                <circle cx="59.4121" cy="60.2244" r="36.014" transform="rotate(117.146 59.4121 60.2244)" fill="url(#paint0_linear_16250_34655)" />
                <circle cx="5.14479" cy="35.5409" r="5.14479" fill="#1CA78B" />
                <circle cx="112.222" cy="43.5798" r="2.25084" fill="#D9D9D9" />
                <circle cx="7.07428" cy="84.4171" r="1.2862" fill="#D9D9D9" />
                <circle cx="82.6395" cy="12.0675" r="1.60775" fill="#F8A629" />
                <circle cx="116.081" cy="17.2124" r="0.964647" fill="#4C1961" />
                <circle cx="29.9047" cy="111.749" r="2.25084" fill="#01A7DB" />
                <circle cx="46.3038" cy="2.74245" r="1.92929" fill="#4C1961" />
                <path fill-rule="evenodd" clip-rule="evenodd" d="M76.5762 51.0886L55.7861 72.7122C54.6285 73.916 52.7766 73.916 51.619 72.7122L41.2471 61.8309C40.0895 60.6271 40.0895 58.6825 41.2471 57.4784C42.4047 56.2746 44.2566 56.2746 45.4142 57.4784L53.7488 66.1833L72.4552 46.7361C73.6128 45.5323 75.4647 45.5323 76.6223 46.7361C77.7337 47.9399 77.7337 49.8848 76.5761 51.0886H76.5762Z" fill="white" />
                <path d="M93.5717 26.5375L94.527 29.1192L97.1087 30.0745L94.527 31.0298L93.5717 33.6115L92.6164 31.0298L90.0347 30.0745L92.6164 29.1192L93.5717 26.5375Z" fill="#D9D9D9" />
                <path d="M24.1166 89.562L25.072 92.1437L27.6537 93.099L25.072 94.0543L24.1166 96.6361L23.1613 94.0543L20.5796 93.099L23.1613 92.1437L24.1166 89.562Z" fill="#D9D9D9" />
                <defs>
                  <linearGradient id="paint0_linear_16250_34655" x1="59.4121" y1="24.2104" x2="59.4121" y2="96.2384" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#1A9E7D" />
                    <stop offset="1" stop-color="#60CE60" />
                  </linearGradient>
                </defs>
              </svg>

              <h1 className="text-2xl sm:text-3xl font-semibold text-white mb-2 sm:mb-3 font-clash">
                Congratulations!🎊
              </h1>
              <p className="text-[#818181] text-xs leading-[18px] max-w-[369px] px-2 sm:px-0">
                Your game with the ticket number <span className="font-white font-semibold text-white">#85478584</span> has successfully been entered for the <span className="font-white font-semibold text-white">Salary 4 Life </span> game show, you will receive an update on the next step shortly. See below your details.
              </p>

              <div className="mt-[18px]">
                <svg width="379" height="1" viewBox="0 0 379 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <line y1="0.7" x2="379" y2="0.7" stroke="white" stroke-opacity="0.6" stroke-width="0.6" stroke-dasharray="4 4" />
                </svg>
              </div>

            </div>

            <div className="mt-[21px] space-y-5">
              <div className="flex w-full gap-3 items-center justify-between">
                <p className="text-[#818181] text-xs">
                  Transaction ID
                </p>
                <p className="text-white text-sm">
                  S4L487484
                </p>
              </div>

              <div className="flex w-full gap-3 items-center justify-between">
                <p className="text-[#818181] text-xs">
                  Station:
                </p>
                <p className="text-white text-sm">
                  The Beat 99.9FM
                </p>
              </div>



              <div className="flex w-full gap-3 items-center justify-between">
                <p className="text-[#818181] text-xs">
                  Tickets Purchased:
                </p>
                <p className="text-white text-sm">
                  13
                </p>
              </div>


              <div className="flex w-full gap-3 items-center justify-between">
                <p className="text-[#818181] text-xs">
                  Amount:
                </p>
                <p className="text-white text-sm">
                  N78,099.38
                </p>
              </div>

              <div className="flex w-full gap-3 items-center justify-between">
                <p className="text-[#818181] text-xs">
                  Date:
                </p>
                <p className="text-white text-sm">
                  April 5th, 2025
                </p>
              </div>

            </div>

            <div className="mt-[21px]">
              <svg width="379" height="1" viewBox="0 0 379 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line y1="0.85" x2="379" y2="0.85" stroke="white" stroke-opacity="0.2" stroke-width="0.3" />
              </svg>
            </div>
            <div className="mt-[14px] flex items-center justify-center">
              <p className="text-[#818181] text-xs text-center max-w-[299px]">🎉 Your name could be on the winners list! Listen in on <span className="font-white font-semibold text-white">Beats 99.9FM Lagos.</span></p>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4 w-full mt-14">
              <Link href="/" className="flex-1">
                <Button
                  variant="outlined"
                  className="w-full bg-transparent border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white h-12 text-sm"
                >
                  Okay
                </Button>
              </Link>
              <Link href="/" className="flex-1">
                <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white h-12 text-sm">
                  Play Again
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
