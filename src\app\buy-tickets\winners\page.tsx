"use client"

import { ChevronLeft, ChevronRight, Search } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button, Input } from "@/components/core"



export default function HomePage() {
  const [currentPage, setCurrentPage] = useState(1)
  const [entriesPerPage, setEntriesPerPage] = useState(10)

  // Sample winners data
  const winnersData = [
    {
      date: "29-04-2022",
      winner: "Chuke*****com",
      stake: "₦1000.00",
      amountWon: "₦10,800,000.00",
      gameType: "Salary for life",
    },
    {
      date: "29-04-2022",
      winner: "Chuke*****com",
      stake: "₦1000.00",
      amountWon: "₦10,800,000.00",
      gameType: "Salary for life",
    },
    {
      date: "29-04-2022",
      winner: "Chuke*****com",
      stake: "₦200.00",
      amountWon: "₦10,800,000.00",
      gameType: "Salary for life",
    },
    {
      date: "29-04-2022",
      winner: "Chuke*****com",
      stake: "₦100.00",
      amountWon: "₦200,000.00",
      gameType: "Salary for life",
    },
    {
      date: "29-04-2022",
      winner: "Chuke*****com",
      stake: "₦1000.00",
      amountWon: "₦200,000.00",
      gameType: "Salary for life",
    },
    {
      date: "29-04-2022",
      winner: "Chuke*****com",
      stake: "₦1000.00",
      amountWon: "₦50,000.00",
      gameType: "Salary for life",
    },
    {
      date: "29-04-2022",
      winner: "Chuke*****com",
      stake: "₦1000.00",
      amountWon: "₦50,000.00",
      gameType: "Salary for life",
    },
    {
      date: "29-04-2022",
      winner: "Chuke*****com",
      stake: "₦1000.00",
      amountWon: "₦50,000.00",
      gameType: "Salary for life",
    },
    {
      date: "29-04-2022",
      winner: "Chuke*****com",
      stake: "₦1000.00",
      amountWon: "₦50,000.00",
      gameType: "Salary for life",
    },
  ]

  const totalEntries = 200
  const totalPages = Math.ceil(totalEntries / entriesPerPage)

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return
    setCurrentPage(page)
  }


  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background with lottery header */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/background.png')",
          backgroundSize: "cover",
        }}
      ></div>

      {/* Lottery Header Banner */}
      <div
        className="relative h-[200px] sm:h-[250px] md:h-[314px] bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/lottery-header.png')",
          backgroundSize: "cover",
        }}
      >
        {/* Header Navigation */}
        <header className="relative z-10 flex flex-col sm:flex-row items-center justify-between px-4 sm:px-8 lg:px-[92px] py-4 sm:py-[20px] bg-[#000000CC] gap-4 sm:gap-0">
          <Link href="/" className="text-white text-xl sm:text-2xl font-bold font-clash">
            S4L Golden Hour
          </Link>

          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-8 items-center w-full sm:w-auto">
            <nav className="flex items-center space-x-4 sm:space-x-8 text-white text-sm order-2 sm:order-1">
              <Link href="/buy-tickets/winners" className="hover:text-yellow-300 transition-colors">
                Winners
              </Link>
              <Link href="/buy-tickets/results" className="hover:text-yellow-300 transition-colors text-[#818181]">
                Results
              </Link>
            </nav>

            <Link href="/buy-tickets/cashout" className="order-1 sm:order-2">
              <Button className="bg-white text-[#4C1961] px-6 sm:px-12 py-2 sm:py-3 rounded-lg font-bold text-sm">
                Cashout
              </Button>
            </Link>
          </div>
        </header>

      </div>

      {/* Main Content */}
      <main className="relative z-10 flex items-start justify-center pt-4 sm:pt-8 px-4 -mt-16 sm:-mt-44">
        {/* Ticket Purchase Card */}
        <div className="relative w-full max-w-[796px]">
          {/* Glow effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-lg blur-sm opacity-75"></div>

          <div className="relative py-6 sm:py-[38px] px-4 sm:px-8 w-full bg-[#000410] backdrop-blur-sm border border-gray-800/50 shadow-xl rounded-[10px]">
            <div className="text-center mb-12">
              <h1 className="text-5xl font-bold text-white mb-4 font-clash">.Winners.</h1>
              <p className="text-gray-400 text-sm">
                Meet our lucky winners! <span className="text-yellow-400">✨</span> See who walked away with amazing prizes.
              </p>
            </div>

            {/* Winners Table */}
            <div className="relative">

              <div className="relative bg-[#0A0B1E] rounded-[15px] overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-[#15171D]">
                        <th className="py-4 px-6 text-left text-sm font-semibold text-gray-300">Date</th>
                        <th className="py-4 px-6 text-left text-sm font-semibold text-gray-300">Winner</th>
                        <th className="py-4 px-6 text-left text-sm font-semibold text-gray-300">Stake</th>
                        <th className="py-4 px-6 text-left text-sm font-semibold text-gray-300">Amount won</th>
                        <th className="py-4 px-6 text-left text-sm font-semibold text-gray-300">Game type</th>
                      </tr>
                    </thead>
                    <tbody>
                      {winnersData.map((winner, index) => (
                        <tr
                          key={index}
                          className={`border-t border-gray-800 ${index % 2 === 0 ? "bg-[#15171D]" : "bg-[#000410]"}`}
                        >
                          <td className="py-4 px-6 text-xs text-gray-300">{winner.date}</td>
                          <td className="py-4 px-6 text-xs text-gray-300">{winner.winner}</td>
                          <td className="py-4 px-6 text-xs text-gray-300">{winner.stake}</td>
                          <td className="py-4 px-6 text-xs text-green-400 font-medium">{winner.amountWon}</td>
                          <td className="py-4 px-6 text-xs text-gray-300">{winner.gameType}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                <div className="flex items-center justify-between px-6 py-4 bg-[#000410] border-gray-800 mt-[38px]">
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-gray-400">Show</span>
                    <select
                      value={entriesPerPage}
                      onChange={(e) => setEntriesPerPage(Number(e.target.value))}
                      className="bg-[#1A1B2E] border border-gray-700 text-white rounded px-2 py-1 text-xs"
                    >
                      <option value={10}>10</option>
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                    </select>
                    <span className="text-xs text-gray-400">
                      Showing 1 to {entriesPerPage} out of {totalEntries} entries
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`p-2 rounded ${currentPage === 1
                        ? "text-gray-600 cursor-not-allowed"
                        : "text-gray-400 hover:bg-[#15171D] bg-[#15171D] hover:text-white"
                        }`}
                    >
                      <ChevronLeft className="w-5 h-5" />
                    </button>

                    {[...Array(Math.min(5, totalPages))].map((_, i) => {
                      const pageNumber = i + 1
                      return (
                        <button
                          key={i}
                          onClick={() => handlePageChange(pageNumber)}
                          className={`w-8 h-8 flex items-center justify-center rounded text-[13px] ${currentPage === pageNumber
                            ? "bg-white text-[#15171D]"
                            : "text-gray-400 hover:bg-[#1A1B2E] bg-[#15171D] hover:text-white"
                            }`}
                        >
                          {pageNumber}
                        </button>
                      )
                    })}

                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className={`p-2 rounded ${currentPage === totalPages
                        ? "text-gray-600 cursor-not-allowed"
                        : "text-gray-400 hover:bg-[#15171D] bg-[#15171D] hover:text-white"
                        }`}
                    >
                      <ChevronRight className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

