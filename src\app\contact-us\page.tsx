"use client"

import <PERSON><PERSON>ead<PERSON> from '@/components/layout/MainHeader'
import React from 'react'
import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Phone, Mail, MapPin, Facebook, Twitter, Instagram, Send, MailIcon } from "lucide-react"
import { Input, Textarea, Form, FormControl, FormField, FormItem, FormMessage, Button, ErrorModal, LinkButton } from '@/components/core'
import { usePostContactForm } from '../misc/api/postFillContactForm'
import ContactUsSuccess from '../misc/modals/ContactSuccessModal'
import { formatAxiosErrorMessage } from '@/utils/errors'
import { AxiosError } from 'axios'

const formSchema = z.object({
    full_name: z.string().min(2, { message: "Full name must be at least 2 characters" }),
    email: z.string().email({ message: "Please enter a valid email address" }),
    phone_no: z.string().min(11, { message: "Please enter a valid phone number" }),
    message: z.string().min(5, { message: "Message must be at least 5 characters" }),
})

type FormValues = z.infer<typeof formSchema>

const Page = () => {
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [isContactUsSuccess, setContactUsSuccess] = useState(false)
    const { mutate: contact_form } = usePostContactForm()
    const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),

    })

    const onSubmit = (data: FormValues) => {
        contact_form(data, {
            onSuccess: () => {
                setContactUsSuccess(true)
            },
            onError: (error: unknown) => {
                const errorMsg = formatAxiosErrorMessage(error as AxiosError);
                setErrorMessage(errorMsg);
                setIsErrorModalOpen(true);
            },
        })
    }
    const closeModals = () => {
        setContactUsSuccess(false)
    }

    return (
        <main className="bg-[url('/images/bgImage.png')] min-h-screen bg-no-repeat text-white [background-size:cover] pt-5 md:pt-14 ">
            <section className='px-5 md:px-10 xl:px-32 '>
                <MainHeader />
                <div className='mt-12'>
                    <h1 className='font-clash text-2xl md:text-4xl font-bold text-center'>Contact Us</h1>
                    <p className='text-sm md:text-lg font-medium text-center mt-3'>Do you have any question? Write us a message!</p>
                </div>

                <div className="w-full max-w-6xl mx-auto mt-11 md:p-3 rounded-lg md:bg-[#141414]">
                    <div className="flex flex-col lg:flex-row bg-[#141414] rounded-lg overflow-hidden">
                        {/* Left Section - Contact Information */}
                        <div className="relatie w-full xl:w-2/5 bg-black p-8 relative overflow-hidden">
                            <div>
                                <h2 className="text-xl md:text-2xl xl:text-3xl font-bold text-white mb-4">Contact Information</h2>
                                <p className="text-gray-300 mb-8 text-sm md:text-base">Talk to us via our social media platforms or send us a message.</p>
                            </div>

                            <div className="space-y-6 text-xs md:text-base mt-24">
                                <div className="flex items-center text-white">
                                    <Phone className="h-5 w-5 mr-4" />
                                    <span>+234 ************</span>
                                </div>

                                <div className="flex items-center text-white">
                                    <Mail className="h-5 w-5 mr-4" />
                                    <span><EMAIL></span>
                                </div>

                                <div className="flex items-start text-white">
                                    <MapPin className="h-5 w-5 mr-4 mt-1 flex-shrink-0" />
                                    <span>27, Alara Street, off Commercial Avenue, Herbert Macaulay, Sabo, Yaba, Lagos.</span>
                                </div>
                            </div>

                            <div className="xl:absolute xl:bottom-8 xl:left-8 mt-20 flex space-x-4">
                                <LinkButton href="https://www.facebook.com/profile.php?id=61558020097928&mibextid=rS40aB7S9Ucbxw6v" className="bg-white rounded-full p-2 hover:bg-gray-200 transition-colors">
                                    <Facebook className="h-5 w-5 text-black" />
                                </LinkButton>
                                <LinkButton href="https://x.com/mtnwinwise?s=11&t=hiO-PpveLL2MH-_-IPymTg" className="bg-white rounded-full p-2 hover:bg-gray-200 transition-colors">
                                    <Twitter className="h-5 w-5 text-black" />
                                </LinkButton>
                                <LinkButton href="https://www.instagram.com/mtnsalaryforlife?igsh=aDJ5bmtvbm9rc2hl&utm_source=qr" className="bg-white rounded-full p-2 hover:bg-gray-200 transition-colors">
                                    <Instagram className="h-5 w-5 text-black" />
                                </LinkButton>
                                <LinkButton href="mailto:<EMAIL>" className="bg-white rounded-full p-2 hover:bg-gray-200 transition-colors">
                                    <MailIcon className="h-5 w-5 text-black" />
                                </LinkButton>
                            </div>
                            <div className="absolute size-24 md:size-48 rounded-full bottom-[-10%] right-[-10%] bg-[#1A1A1A] "></div>
                            <div className="absolute size-16 md:size-32 rounded-full bg-[#48484880] bottom-[2%] md:bottom-[10%] right-[2%] md:right-[10%]"></div>
                        </div>

                        {/* Right Section - Form */}

                        <div className="w-full xl:w-3/5 md:p-2 xl:p-8 mt-3 md:mt-0 mb-3 lg:mb-0">
                            <div className='h-full bg-black max-w-[507px] p-4 xl:p-8 rounded-xl'>
                                <h2 className="font-medium text-white mb-8">Get in touch</h2>
                                <Form {...form}>
                                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                        <FormField
                                            control={form.control}
                                            name="full_name"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormControl>
                                                        <Input
                                                            placeholder="Full name"
                                                            className="bg-[#222222] border-0 text-white h-14 rounded-md focus-visible:ring-0 focus-visible:ring-offset-0"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <FormMessage className="text-red-400" />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="email"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormControl>
                                                        <Input
                                                            placeholder="Email"
                                                            type="email"
                                                            className="bg-[#222222] border-0 text-white h-14 rounded-md focus-visible:ring-0 focus-visible:ring-offset-0"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <FormMessage className="text-red-400" />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="phone_no"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormControl>
                                                        <Input
                                                            placeholder="Phone no"
                                                            className="bg-[#222222] border-0 text-white h-14 rounded-md focus-visible:ring-0 focus-visible:ring-offset-0"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <FormMessage className="text-red-400" />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="message"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormControl>
                                                        <Textarea
                                                            placeholder="Message"
                                                            className="bg-[#222222] border-0 text-white min-h-[130px] rounded-md resize-none focus-visible:ring-0 focus-visible:ring-offset-0"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <FormMessage className="text-red-400" />
                                                </FormItem>
                                            )}
                                        />

                                        <Button
                                            type="submit"
                                            disabled={isSubmitting}
                                            className="w-full md:w-auto bg-white text-black hover:bg-gray-200 h-12 px-10 rounded-md font-medium"
                                        >
                                            {isSubmitting ? "Submitting..." : "Submit"}
                                        </Button>
                                    </form>
                                </Form>
                            </div>
                        </div>

                    </div>
                </div>

            </section>
            <ContactUsSuccess
                isContactUsSuccess={isContactUsSuccess}
                setContactUsSuccess={closeModals}
            />

            {isErrorModalOpen && (
                <ErrorModal
                    isErrorModalOpen={isErrorModalOpen}
                    setErrorModalState={setIsErrorModalOpen}
                    subheading={errorMessage || 'An error occurred. Please try again.'}
                >
                    <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                        <Button
                            className="grow bg-red-950 text-base"
                            size="lg"
                            onClick={() => setIsErrorModalOpen(false)}
                        >
                            Okay
                        </Button>
                    </div>
                </ErrorModal>
            )}

        </main>
    )
}

export default Page