import type { Metada<PERSON> } from "next";
import { Inter, Montserrat } from "next/font/google";
import localFont from "next/font/local";
import "./globals.css";
import { cn } from "@/utils/classNames";
import ReactQueryProvider from "@/lib/reactQuery";
import { Whatsapp } from "@/components/icons";
import { Wrapper } from "@/components/Wrapper";

const fontClash = localFont({
  src: "./fonts/ClashDisplay-Variable.woff2",
  variable: "--font-clash",
  display: "swap",
});

const montserrat = Montserrat({ subsets: ["latin"] });

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Salary4Lyf game show",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <ReactQueryProvider>
        <body className={cn(montserrat.className, fontClash.variable)}>
          <Wrapper>{children}</Wrapper>
          <div>
            <a
              href="https://api.whatsapp.com/send?phone=2348107614479&text=Hello%20Salary4Lyf%20Team%2C%20I%20have%20a%20question%20regarding%20your%20services."
              target="_blank"
              className="fixed bottom-52 right-4 z-[**********] h-16 w-16 rounded-full bg-[#25d366] flex items-center justify-center shadow-lg transition-colors p-4"
            >
              <Whatsapp height="250" width="250" className="text-white" />
            </a>
          </div>
        </body>
      </ReactQueryProvider>
    </html>
  );
}
