import { adminAxios } from "@/lib/axios";
import { useMutation } from "react-query";




type ScratchCardPartnerFormData = {
    name: string;
    email: string;
    phone_number: string;
    location: string;
}

const postScratchCardPartnerForm = async (data: ScratchCardPartnerFormData) => {
    const response = await adminAxios.post('/liberty/scratch_card_partnership/', data)
    return response.data
}

export const usePostScratchCardPartnerForm = () => {
    return useMutation({
        mutationFn: postScratchCardPartnerForm,
        mutationKey: 'ScratchCardPartnerForm'
    })
}