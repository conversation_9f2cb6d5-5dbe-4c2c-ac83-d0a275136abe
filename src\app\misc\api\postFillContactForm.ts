import { adminAxios } from "@/lib/axios";
import { useMutation } from "react-query";




type ContactFormData = {
    full_name: string;
    email: string;
    phone_no: string;
    message: string;
}

const postContactForm = async (data: ContactFormData) => {
    const response = await adminAxios.post('/liberty/fill_contact_form/', data)
    return response.data
}

export const usePostContactForm = () => {
    return useMutation({
        mutationFn: postContactForm,
        mutationKey: 'contactForm'
    })
}