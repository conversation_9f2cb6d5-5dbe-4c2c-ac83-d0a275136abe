import { adminAxios } from "@/lib/axios"
import { useMutation } from "react-query";

type ParticipantDTO = {
    name: string;
    email: string;
    phone_number: string;
    location: string;
};

type ParticipantDetails = {
    id: number;
    name: string;
    email: string;
    phone_number: string;
    date_created: string; // ISO timestamp
};

type CreateParticipantResponse = {
    message: string;
    participant_details: ParticipantDetails;
};


const postParticipantDetails = async (data:ParticipantDTO)=>{
    const response = await adminAxios.post('/liberty/create_participant/', data)
    return response.data as CreateParticipantResponse
}

export const usePostParticipant = () => {
     return useMutation({
        mutationFn: postParticipantDetails,
        mutationKey:'participantDetails'
        
     })
}