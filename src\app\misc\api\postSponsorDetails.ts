import { adminAxios } from "@/lib/axios"
import { useMutation } from "react-query";

export interface CreateSponsorDTO {
    brand_name: string;
    designation: string;
    email: string;
    phone_number: string;
  }
  
  export interface SponsorDetails {
    id: number;
    brand_name: string;
    designation: string;
    email: string;
    phone_number: string;
    date_created: string;
  }
  
  export interface CreateSponsorResponse {
    message: string;
    sponsor_details: SponsorDetails;
  }
  
const postSponsorDetails = async(data:CreateSponsorDTO) => {
    const response = await adminAxios.post('/liberty/create_sponsor/', data)
    return response.data as SponsorDetails
}

export const usePostSponsor = () => {
    return useMutation({
        mutationKey: 'postSponsor',
        mutationFn: postSponsorDetails,
    })
}
