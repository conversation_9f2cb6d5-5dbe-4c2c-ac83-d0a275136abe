import { Button, <PERSON><PERSON>, <PERSON><PERSON>B<PERSON>, DialogContent, Link<PERSON>utton } from "@/components/core";
import { ScuccessIcon } from "@/components/icons";

interface ContactUsSuccessProps {
    isContactUsSuccess: boolean;
    setContactUsSuccess: () => void
}

function ContactUsSuccess({
    isContactUsSuccess,
    setContactUsSuccess,
}: ContactUsSuccessProps) {

    return (
        <Dialog open={isContactUsSuccess}>
            <DialogContent className='bg-[#000410] border-[0.5px] border-[#00E2C6] pb-16'>
                <DialogBody>
                    <div className="flex justify-center items-center">
                        <ScuccessIcon />
                    </div>
                    <h1 className="text-center text-2xl font-semibold font-clash pt-11">Success</h1>
                    <p className="text-center text-[#818181] text-sm pt-2">Ms<PERSON>ge Submitted Successfully</p>
                    <Button
                        onClick={setContactUsSuccess}
                        className='bg-gradient-to-r from-[#4C1961] to-[#9602AD] w-full mt-10 font-semibold py-3 text-lg'
                    >
                        okay
                    </Button>
                </DialogBody>
            </DialogContent>
        </Dialog>
    )
}

export default ContactUsSuccess