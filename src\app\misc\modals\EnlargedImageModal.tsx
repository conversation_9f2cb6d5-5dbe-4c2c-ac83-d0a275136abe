import React, { useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface EnlargedImageModalProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
}

const EnlargedImageModal: React.FC<EnlargedImageModalProps> = ({ isOpen, onClose, imageUrl }) => {
  // Handle escape key press
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose()
    }
    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }
    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="relative w-[95%] max-w-5xl mx-4 md:mx-8"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {/* Close button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onClose}
              className="absolute -top-12 right-0 text-white text-3xl hover:text-gray-300 transition-colors duration-200 z-10"
              aria-label="Close modal"
            >
              ✕
            </motion.button>

            {/* Image container */}
            <div className="relative rounded-xl overflow-hidden shadow-2xl">
              <motion.img
                initial={{ scale: 0.95 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.3 }}
                src={imageUrl}
                alt="Enlarged view"
                className="w-full h-auto object-contain max-h-[85vh]"
                loading="eager"
              />
              
              {/* Loading overlay */}
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            </div>

            {/* Touch instructions for mobile */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-sm opacity-70 md:hidden">
              Tap outside to close
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default EnlargedImageModal 