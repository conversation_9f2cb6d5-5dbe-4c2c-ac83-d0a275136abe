'use client'

import { <PERSON><PERSON>, Dialog, DialogBody, DialogClose, DialogContent, DialogHeader, DialogTitle, Input, ErrorModal, FormError, Checkbox } from '@/components/core'
import { CloseIcon } from '@/components/icons';
import React, { useState } from 'react'
import { usePostParticipant } from '../api/postParticipantDetails';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { AxiosError } from 'axios';
import { formatAxiosErrorMessage } from '@/utils/errors';
import ParticipantSuccess from './ParticipantSuccessModal';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core/Select';
import { States } from '../mockdata';
import { useRouter } from 'next/navigation';

const participantSchema = z.object({
    name: z.string().min(1, "Name is required"),
    email: z.string().email("Invalid email format"),
    phone_number: z.string().min(10, "Enter a valid phone number"),
    location: z.string().min(1, "select a state"),
    consent: z.boolean().refine(val => val === true, {
        message: "You must agree to the terms and conditions"
    })
});

type ParticipantFormData = z.infer<typeof participantSchema>;

interface ParticipantDetailsProps {
    isParticipantDetails: boolean;
    setParticipantDetails: React.Dispatch<React.SetStateAction<boolean>>;
    heading: string;
    closeLink?: string;
    referral_code?: string;
}

function ParticipantDetails({
    isParticipantDetails,
    setParticipantDetails,
    heading,
    closeLink,
    referral_code
}: ParticipantDetailsProps) {
    const router = useRouter()
    const [isParticipantSuccess, setParticipantSuccess] = useState(false);
    const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');

    const { mutate: participantDetail, isLoading } = usePostParticipant();

    const {
        register,
        handleSubmit,
        control,
        formState: { errors },
    } = useForm<ParticipantFormData>({
        resolver: zodResolver(participantSchema),
        defaultValues: {
            consent: false
        }
    });

    const onSubmit = (data: ParticipantFormData) => {
        const updateData = {
            ...data,
            referrer: referral_code
        }

        participantDetail(updateData, {
            onSuccess: () => {
                setParticipantSuccess(true);
            },
            onError: (error: unknown) => {
                const errorMsg = formatAxiosErrorMessage(error as AxiosError);
                setErrorMessage(errorMsg);
                setIsErrorModalOpen(true);
            },
        });
    };


    const closeModals = () => {
        setParticipantSuccess(false)
        setParticipantDetails(false)
    }

    return (
        <>
            <Dialog open={isParticipantDetails} onOpenChange={setParticipantDetails}>
                <DialogContent className='bg-[#000410] border-[0.5px] border-[#68107E]'>
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className='font-clash text-2xl font-semibold'>{heading}</h1>
                            <p className='text-left text-xs text-[#818181]'>Fill the details below to become a participant of the salary for life game show. </p>
                        </DialogTitle>
                        {
                            closeLink === "PARTICIPANT" ? (
                                <Button onClick={
                                    () => {
                                        router.push('/')

                                    }
                                }>
                                    <CloseIcon />
                                </Button>
                            ) : (
                                <DialogClose><CloseIcon /></DialogClose>
                            )
                        }

                    </DialogHeader>
                    <DialogBody>
                        <div className='mb-20'>
                            <form onSubmit={handleSubmit(onSubmit)}>
                                <div className="mt-6">
                                    <label className="text-sm mt-4">Name</label>
                                    <Input
                                        className="bg-[#15171D] border-none py-3 mt-3"
                                        placeholder='Enter Name'
                                        {...register("name")}
                                    />
                                    {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>}
                                </div>
                                <div className="mt-6">
                                    <label className="text-sm mt-4">Email</label>
                                    <Input
                                        className="bg-[#15171D] border-none py-3 mt-3"
                                        placeholder='Enter your email'
                                        {...register("email")}
                                    />
                                    {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>}
                                </div>
                                <div className="mt-6">
                                    <label className="text-sm mt-4">Phone Number</label>
                                    <Input
                                        className="bg-[#15171D] border-none py-3 mt-3"
                                        placeholder='Enter your phone number'
                                        {...register("phone_number")}
                                    />
                                    {errors.phone_number && <p className="text-red-500 text-xs mt-1">{errors.phone_number.message}</p>}
                                </div>
                                <div className="mt-4 top-52">
                                    <label className="mb-1 text-xs text-white">Location (where you live)</label>
                                    <Controller
                                        control={control}
                                        name="location"
                                        render={({ field: { onChange, value, ref } }) => (
                                            <Select value={value} onValueChange={onChange}>
                                                <SelectTrigger
                                                    className='h-12 rounded-lg bg-[#15171D] text-white border-[#68107E]'
                                                    iconClassName="fill-white"
                                                    id="location"
                                                    ref={ref}
                                                >
                                                    <SelectValue className='text-white' placeholder="Select State of residence" />
                                                </SelectTrigger>
                                                <SelectContent className='bg-[#15171D] !h-60 overflow-auto text-white border-[#68107E]'>
                                                    {States?.map(({ value, id }) => (
                                                        <SelectItem
                                                            key={id}
                                                            value={value}
                                                            className="text-white hover:bg-[#9602AD] focus:bg-[#9602AD] focus:text-white cursor-pointer"
                                                        >
                                                            {value}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}
                                    />
                                    {errors.location && errors?.location && <FormError errorMessage={errors?.location.message} />}
                                </div>

                                <div className="mt-6 flex items-start gap-3">
                                    <Controller
                                        control={control}
                                        name="consent"
                                        render={({ field: { onChange, value, ref } }) => (
                                            <Checkbox
                                                id="consent"
                                                checked={value}
                                                onCheckedChange={onChange}
                                                ref={ref}
                                                className="mt-1"
                                            />
                                        )}
                                    />
                                    <label htmlFor="consent" className="text-xs text-white cursor-pointer">
                                        I agree to the processing of my personal data in accordance with the{" "}
                                        <a
                                            href="https://www.wisewinn.com/terms"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-[#9602AD] hover:underline"
                                        >
                                            Terms of Service
                                        </a>
                                    </label>
                                </div>
                                {errors.consent && <p className="text-red-500 text-xs mt-1 ml-7">{errors.consent.message}</p>}

                                <Button
                                    type="submit"
                                    className='bg-gradient-to-r from-[#4C1961] to-[#9602AD] w-full mt-10 font-semibold py-3 text-lg flex items-center justify-center'
                                    disabled={isLoading}
                                >
                                    {isLoading ? "Submitting..." : "Submit"}
                                </Button>
                            </form>
                        </div>
                    </DialogBody>
                </DialogContent>
            </Dialog>

            <ParticipantSuccess
                isParticipantSuccess={isParticipantSuccess}
                setParticipantSuccess={closeModals}
            />

            {isErrorModalOpen && (
                <ErrorModal
                    isErrorModalOpen={isErrorModalOpen}
                    setErrorModalState={setIsErrorModalOpen}
                    subheading={errorMessage || 'An error occurred. Please try again.'}
                >
                    <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                        <Button
                            className="grow bg-red-950 text-base"
                            size="lg"
                            onClick={() => setIsErrorModalOpen(false)}
                        >
                            Okay
                        </Button>
                    </div>
                </ErrorModal>
            )}
        </>
    )
}

export default ParticipantDetails