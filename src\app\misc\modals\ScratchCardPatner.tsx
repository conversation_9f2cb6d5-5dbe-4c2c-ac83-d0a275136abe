'use client'

import { <PERSON>ton, Dialog, DialogBody, DialogClose, DialogContent, DialogHeader, DialogTitle, Input, ErrorModal, FormError } from '@/components/core'
import { CloseIcon } from '@/components/icons';
import React, { useState } from 'react'
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { AxiosError } from 'axios';
import { formatAxiosErrorMessage } from '@/utils/errors';
import ParticipantSuccess from './ParticipantSuccessModal';
import { usePostScratchCardPartnerForm } from '../api/PostScrathCardPart';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core/Select';
import { States } from '../mockdata';

const participantSchema = z.object({
    name: z.string().min(1, "Name is required"),
    email: z.string().email("Invalid email format"),
    phone_number: z.string().min(10, "Enter a valid phone number"),
    location: z.string().min(1, "select a state")
});

type ParticipantFormData = z.infer<typeof participantSchema>;

interface ScratchCardPartnerDetProps {
    isScratchCardPartnerDet: boolean;
    setScratchCardPartnerDet: React.Dispatch<React.SetStateAction<boolean>>;
    heading: string;
}

function ScratchCardPartnerDet({
    isScratchCardPartnerDet,
    setScratchCardPartnerDet,
    heading
}: ScratchCardPartnerDetProps) {
    const [isParticipantSuccess, setParticipantSuccess] = useState(false);
    const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');

    const { mutate: scratchCarddet, isLoading } = usePostScratchCardPartnerForm();

    const {
        register,
        handleSubmit,
        control,
        formState: { errors },
    } = useForm<ParticipantFormData>({
        resolver: zodResolver(participantSchema),
    });

    const onSubmit = (data: ParticipantFormData) => {
        scratchCarddet(data, {
            onSuccess: () => {
                setParticipantSuccess(true);
            },
            onError: (error: unknown) => {
                const errorMsg = formatAxiosErrorMessage(error as AxiosError);
                setErrorMessage(errorMsg);
                setIsErrorModalOpen(true);
            },
        });
    };


    const closeModals = () => {
        setParticipantSuccess(false)
        setScratchCardPartnerDet(false)
    }

    return (
        <>
            <Dialog open={isScratchCardPartnerDet} onOpenChange={setScratchCardPartnerDet}>
                <DialogContent className='bg-[#000410] border-[0.5px] border-[#68107E]'>
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className='font-clash text-2xl font-semibold'>{heading}</h1>
                            <p className='text-left text-xs text-[#818181]'>Fill the details below to become a participant of the salary for life game show. </p>
                        </DialogTitle>
                        <DialogClose><CloseIcon /></DialogClose>
                    </DialogHeader>
                    <DialogBody>
                        <div className='mb-20'>
                            <form onSubmit={handleSubmit(onSubmit)}>
                                <div className="mt-6">
                                    <label className="text-sm mt-4">Name</label>
                                    <Input
                                        className="bg-[#15171D] border-none py-3 mt-3"
                                        placeholder='Enter Name'
                                        {...register("name")}
                                    />
                                    {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>}
                                </div>
                                <div className="mt-6">
                                    <label className="text-sm mt-4">Email</label>
                                    <Input
                                        className="bg-[#15171D] border-none py-3 mt-3"
                                        placeholder='Enter your email'
                                        {...register("email")}
                                    />
                                    {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>}
                                </div>
                                <div className="mt-6">
                                    <label className="text-sm mt-4">Phone Number</label>
                                    <Input
                                        className="bg-[#15171D] border-none py-3 mt-3"
                                        placeholder='Enter your phone number'
                                        {...register("phone_number")}
                                    />
                                    {errors.phone_number && <p className="text-red-500 text-xs mt-1">{errors.phone_number.message}</p>}
                                </div>
                                <div className="mt-4 top-52">
                                    <label className="mb-1 text-xs">Location</label>
                                    <Controller
                                        control={control}
                                        name="location"
                                        render={({ field: { onChange, value, ref } }) => (
                                            <Select value={value} onValueChange={onChange}>
                                                <SelectTrigger
                                                    className='h-12 rounded-lg bg-[#15171D]'
                                                    iconClassName="fill-white"
                                                    id="location"
                                                    ref={ref}
                                                >
                                                    <SelectValue className='text-white' placeholder="Select State" />
                                                </SelectTrigger>
                                                <SelectContent className='bg-[#15171D] !h-60 overflow-auto'>
                                                    {States?.map(({ value, id }) => (
                                                        <SelectItem key={id} value={value}>
                                                            {value}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}
                                    />
                                    {errors.location && errors?.location && <FormError errorMessage={errors?.location.message} />}
                                </div>
                                <Button
                                    type="submit"
                                    className='bg-gradient-to-r from-[#4C1961] to-[#9602AD] w-full mt-10 font-semibold py-3 text-lg flex items-center justify-center'
                                    disabled={isLoading}
                                >
                                    {isLoading ? "Submitting..." : "Submit"}
                                </Button>
                            </form>
                        </div>
                    </DialogBody>
                </DialogContent>
            </Dialog>

            <ParticipantSuccess
                isParticipantSuccess={isParticipantSuccess}
                setParticipantSuccess={closeModals}
            />

            {isErrorModalOpen && (
                <ErrorModal
                    isErrorModalOpen={isErrorModalOpen}
                    setErrorModalState={setIsErrorModalOpen}
                    subheading={errorMessage || 'An error occurred. Please try again.'}
                >
                    <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                        <Button
                            className="grow bg-red-950 text-base"
                            size="lg"
                            onClick={() => setIsErrorModalOpen(false)}
                        >
                            Okay
                        </Button>
                    </div>
                </ErrorModal>
            )}
        </>
    )
}

export default ScratchCardPartnerDet
