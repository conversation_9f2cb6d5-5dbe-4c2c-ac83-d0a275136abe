"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON>ton, Dialog, DialogBody, DialogClose, DialogContent, DialogHeader, DialogTitle, ErrorModal, Input } from "@/components/core";
import { CloseIcon } from "@/components/icons";
import React, { useState } from "react";
import SponsorSuccess from "./SponsorSuccess";
import { usePostSponsor } from "../api/postSponsorDetails";
import { formatAxiosErrorMessage } from "@/utils/errors";
import useErrorModalState from "@/hooks/useErrorModalState";
import { AxiosError } from "axios";

const sponsorSchema = z.object({
    brand_name: z.string().min(1, "Brand Name is required"),
    designation: z.string().min(1, "Designation is required"),
    email: z.string().email("Invalid email format"),
    phone_number: z.string().min(1, "Enter a valid phone number"),
});


type SponsorFormData = z.infer<typeof sponsorSchema>;

interface SponsorDetailsProps {
    isSponsorDetails: boolean;
    setSponsorDetails: React.Dispatch<React.SetStateAction<boolean>>;
    heading: string;
}

function SponsorDetails({ isSponsorDetails, setSponsorDetails, heading }: SponsorDetailsProps) {
    const [isSponsorSuccess, setSponsorSuccess] = useState(false);

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();

    // Use mutation hook for form submission
    const { mutate: sponsorDet, isLoading, isError } = usePostSponsor();

    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<SponsorFormData>({
        resolver: zodResolver(sponsorSchema),
    });

    const onSubmit = (data: SponsorFormData) => {
        sponsorDet(data, {
            onSuccess: () => {
                setSponsorSuccess(true);
            },
            onError: (error: unknown) => {
                const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                openErrorModalWithMessage(errorMessage);
            },
        });
    };

    const closeModals = () => {
        setSponsorSuccess(false)
        setSponsorDetails(false)
    }

    return (
        <div>
            <Dialog open={isSponsorDetails}>
                <DialogContent className="bg-[#000410] border-[0.5px] border-[#68107E]">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="font-clash text-2xl font-semibold">{heading}</h1>
                            <p className="text-left text-xs text-[#818181]">
                                Fill the details below to become a distinguished sponsor for the Salary for Life game show.
                            </p>
                        </DialogTitle>
                        <DialogClose onClick={() => setSponsorDetails(false)} className="!py-0">
                            <CloseIcon />
                        </DialogClose>
                    </DialogHeader>
                    <DialogBody>
                        <div className="mb-20">
                            <form onSubmit={handleSubmit(onSubmit)}>
                                <div className="mt-6">
                                    <label className="text-sm">Brand Name</label>
                                    <Input className="bg-[#15171D] border-none py-3 mt-3" placeholder="Enter brand name" {...register("brand_name")} />
                                    {errors.brand_name && <p className="text-red-500 text-xs mt-1">{errors.brand_name.message}</p>}
                                </div>

                                <div className="mt-6">
                                    <label className="text-sm">Designation</label>
                                    <Input className="bg-[#15171D] border-none py-3 mt-3" placeholder="Enter your designation" {...register("designation")} />
                                    {errors.designation && <p className="text-red-500 text-xs mt-1">{errors.designation.message}</p>}
                                </div>

                                <div className="mt-6">
                                    <label className="text-sm">Email</label>
                                    <Input className="bg-[#15171D] border-none py-3 mt-3" placeholder="Enter your email" {...register("email")} />
                                    {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>}
                                </div>

                                <div className="mt-6">
                                    <label className="text-sm">Phone Number</label>
                                    <Input className="bg-[#15171D] border-none py-3 mt-3" placeholder="Enter your phone number" {...register("phone_number")} />
                                    {errors.phone_number && <p className="text-red-500 text-xs mt-1">{errors.phone_number.message}</p>}
                                </div>

                                <Button
                                    type="submit"
                                    className="bg-gradient-to-r from-[#4C1961] to-[#9602AD] w-full mt-10 font-semibold py-3 text-lg"
                                    disabled={isLoading}
                                >
                                    {isLoading ? "Submitting..." : "Submit"}
                                </Button>
                            </form>
                        </div>
                    </DialogBody>
                </DialogContent>
            </Dialog>



            <SponsorSuccess
                isSponsorSuccess={isSponsorSuccess}
                setSponsorSuccess={closeModals}
            />


            {isErrorModalOpen &&
                <ErrorModal
                    isErrorModalOpen={isErrorModalOpen}
                    setErrorModalState={setErrorModalState}
                    subheading={errorModalMessage || 'Please check your inputs and try again.'}
                >
                    <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                        <Button
                            className="grow bg-red-950 text-base"
                            size="lg"
                            onClick={closeErrorModal}
                        >
                            Okay
                        </Button>
                    </div>
                </ErrorModal>
            }
        </div>
    );
}

export default SponsorDetails;
