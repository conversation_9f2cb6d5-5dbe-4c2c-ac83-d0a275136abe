import { Button, <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Link<PERSON>utton } from "@/components/core";
import { ScuccessIcon } from "@/components/icons";

interface SponsorSuccessProps {
    isSponsorSuccess: boolean;
    setSponsorSuccess: () => void
}

function SponsorSuccess({
    isSponsorSuccess,
    setSponsorSuccess,
}: SponsorSuccessProps) {

    return (
       
            <Dialog open={isSponsorSuccess}>
                <DialogContent className='bg-[#000410] border-[0.5px] border-[#00E2C6] pb-16'>
                    <DialogBody>
                        <div className="flex justify-center items-center">
                            <ScuccessIcon />
                        </div>
                        <h1 className="text-center text-2xl font-semibold font-clash pt-11">Thank You !</h1>
                        <p className="text-center text-[#818181] text-sm pt-2">Thanks for your showing interest to sponsor the salary for life game show, we will reach out to you soon.</p>
                        <Button
                            onClick={setSponsorSuccess}
                            className='bg-gradient-to-r from-[#4C1961] to-[#9602AD] w-full mt-10 font-semibold py-3 text-lg'
                        >
                            okay
                        </Button>
                    </DialogBody>
                </DialogContent>
            </Dialog>
        
    )
}

export default SponsorSuccess