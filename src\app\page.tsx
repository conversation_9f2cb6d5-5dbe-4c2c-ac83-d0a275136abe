"use client"

import { <PERSON><PERSON> } from "@/components/core"
import MainHeader from "@/components/layout/MainHeader"
import { useState, useEffect } from "react"
import NextImage from "next/image"
import ParticipantDetails from "./misc/modals/ParticipantDetails"
import ScratchCardPartnerDet from "./misc/modals/ScratchCardPatner"
import SponsorDetails from "./misc/modals/SponsorDetails"
import EnlargedImageModal from "./misc/modals/EnlargedImageModal"
import { Loader2 } from "lucide-react"
import { useSearchParams } from "next/navigation"

const Page = () => {

  
  const params = useSearchParams();

  const referral_code = params.get('referral') || '';

  console.log(referral_code, "referral")

  
  // Image data with optimized formats and sizes
  const bg_image = [
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992908/img1_big_on5xor.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992908/img2_big_v54dx8.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992909/img3_big_zoh4mp.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992911/img4_big_jtawix.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992908/img5_big_mcdstz.png",
  ]

  const foot_image = [
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992188/img1_djfegq.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992188/img2_qhqnhl.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992188/img3_hisnlk.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992188/img4_pvvbnn.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992188/img5_cc8kfm.png",
  ]

  const enlarged_images = [
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992908/img1_big_on5xor.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992908/img2_big_v54dx8.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992909/img3_big_zoh4mp.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992911/img4_big_jtawix.png",
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992908/img5_big_mcdstz.png",
  ]

  // Use the same images for the background to ensure consistency
  const backgroundImages = [
    "https://res.cloudinary.com/eddiewurld/image/upload/v1747992908/img1_big_on5xor.png", // Keep the default first image
    ...bg_image, // Use the exact same images from foot_image
  ]

  const [SponsorDetail, setSponsorDetails] = useState(false)
  const [ParticipantDetail, setParticipantDetails] = useState(false)
  const [ScratchCardPartner, setScratchCardPartner] = useState(false)
  const [activeImageIndex, setActiveImageIndex] = useState(0)
  const [currentBackground, setCurrentBackground] = useState(backgroundImages[0])
  const [isEnlargedModalOpen, setIsEnlargedModalOpen] = useState(false)
  const [selectedEnlargedImage, setSelectedEnlargedImage] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [bgImageLoaded, setBgImageLoaded] = useState(false)
  const [footerImagesLoaded, setFooterImagesLoaded] = useState(Array(foot_image.length).fill(false))

  // Function to handle image click
  const handleImageClick = (index: number) => {
    const actualIndex = index % foot_image.length
    setActiveImageIndex(actualIndex)
    // Use actualIndex + 1 because backgroundImages has the default image at index 0
    setCurrentBackground(actualIndex === 0 ? backgroundImages[0] : enlarged_images[actualIndex])
    // Show enlarged image
    setSelectedEnlargedImage(enlarged_images[actualIndex])
    setIsEnlargedModalOpen(true)
  }

  // Auto-change background every 8 seconds (slowed down)
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (activeImageIndex + 1) % bg_image.length
      setActiveImageIndex(nextIndex)
      setCurrentBackground(nextIndex === 0 ? backgroundImages[0] : bg_image[nextIndex])
    }, 8000) // Increased from 5000 to 8000 ms

    return () => clearInterval(interval)
  }, [activeImageIndex, bg_image])

  // Preload background image
  useEffect(() => {
    const img = new window.Image()
    img.src = currentBackground
    img.onload = () => {
      setBgImageLoaded(true)
    }
  }, [currentBackground])

  // Preload all images and track loading state
  useEffect(() => {
    let allImagesLoaded = 0
    const totalImages = bg_image.length + foot_image.length

    // Preload background images
    bg_image.forEach((src) => {
      const img = new window.Image()
      img.src = src
      img.onload = () => {
        allImagesLoaded++
        if (allImagesLoaded >= totalImages) {
          setIsLoading(false)
        }
      }
    })

    // Preload footer images
    foot_image.forEach((src, index) => {
      const img = new window.Image()
      img.src = src
      img.onload = () => {
        setFooterImagesLoaded((prev) => {
          const newState = [...prev]
          newState[index] = true
          return newState
        })

        allImagesLoaded++
        if (allImagesLoaded >= totalImages) {
          setIsLoading(false)
        }
      }
    })
  }, [])

  // Generate tiny blur placeholders for each image
  const getBlurDataURL = (index: number) => {
    // Return a colored blur data URL based on the image index
    const colors = ["9602AD", "F69E26", "6A0DAD", "E85D04", "7209B7"]
    const color = colors[index % colors.length]
    return `data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 225' fill='%23${color}'%3E%3Crect width='400' height='225' /%3E%3C/svg%3E`
  }

  return (
    <>
      {/* Loading overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/80 z-50 flex flex-col items-center justify-center">
          <div className="bg-gradient-to-r from-[#F69E26] to-[#9602AD] p-[2px] rounded-full">
            <div className="bg-black rounded-full p-4">
              <Loader2 className="w-12 h-12 text-white animate-spin" />
            </div>
          </div>
          <h2 className="text-white font-bold mt-4 text-xl">Loading Salary 4 Life</h2>
          <div className="w-64 h-2 bg-gray-800 rounded-full mt-4 overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-[#F69E26] to-[#9602AD] rounded-full transition-all duration-300"
              style={{
                width: `${(footerImagesLoaded.filter(Boolean).length / footerImagesLoaded.length) * 100}%`,
              }}
            ></div>
          </div>
        </div>
      )}

      <main
        className="h-screen overflow-auto bg-no-repeat text-white [background-size:cover] pt-5 md:pt-14 relative"
        style={{
          backgroundImage: `url('${currentBackground}')`,
          transition: "background-image 1s ease-in-out", // Add smooth transition
        }}
      >
        {/* Backdrop filter while background image is loading */}
        {!bgImageLoaded && (
          <div className="absolute inset-0 backdrop-blur-xl bg-gradient-to-b from-[#9602AD]/30 to-black/70 z-[1]"></div>
        )}

        {/* Purple gradient overlay */}
        <div className="gradient-overlay"></div>

        {/* Diagonal stripes overlay */}
        <div
          className="diagonal-stripes"
          style={{
            backgroundImage: "url('/images/diagonal-stripes.png')",
            backgroundPosition: "center",
            backgroundSize: "cover",
          }}
        ></div>

        <section className="px-5 md:px-10 xl:px-32 relative z-10">
          <MainHeader />
          <div className="mt-32 items-center">
            <div className="relative mx-auto max-w-xl lg:ml-0 xl:pl-16">
              <div className="relative z-10 text-left lg:pt-4">
                <div className="font-clash font-bold text-white md:text-5xl xl:text-6xl">
                  <h2 className="text-5xl md:text-[82px] lg:text-6xl xl:text-[82px]"> Salary 4 Life</h2>
                  <h2 className="text-5xl md:text-[82px] lg:text-6xl xl:text-[82px] mt-3">
                    <span className="text-[#9602AD]">Game </span> Show
                  </h2>
                </div>
                <p className="my-2 md:my-4 max-w-lg text-[#ffffffcb] text-sm md:text-lg xl:text-base">
                  Be a part of the <span className="text-[#9602AD] font-bold">Salary 4 Life Game Show</span> coming
                  soon. Be part of our auditions in June, Lagos for a chance to be our next salary4life winner of 100m
                </p>
                <p className="text-[#ffffffcb]">Click the participate now button to register</p>
              </div>

              <div className="mt-4 md:mt-[2.625rem] mb-32 md:mb-40">
                {/* <Button
                  className="text-white bg-gradient-to-r w-full block sm:hidden md:w-fit from-[#F69E26] to-[#9602AD] top-0 text-sm md:text-lg py-3 md:px-20"
                  type="button"
                  onClick={() => setParticipantDetails(true)}
                >
                  Participate Now
                </Button> */}

                <Button
                  className="text-white whitespace-nowrap bg-gradient-to-r w-full md:w-fit from-[#F69E26] to-[#9602AD] block sm:hidden top-0 text-sm md:text-lg py-3 md:px-20 relative group hover:scale-105 transition-all duration-300 animate-float shadow-[0_0_15px_rgba(150,2,173,0.3)] hover:shadow-[0_0_25px_rgba(150,2,173,0.5)]"
                  type="button"
                  onClick={() => setParticipantDetails(true)}
                >
                  <span className="relative z-10">Participate Now</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-[#F69E26] to-[#9602AD] rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"></div>
                  <div className="absolute -inset-1 bg-gradient-to-r from-[#F69E26] to-[#9602AD] rounded-md opacity-0 group-hover:opacity-20 transition-opacity duration-300 animate-pulse"></div>
                </Button>
              </div>
              
            </div>
          </div>
        </section>

        <article className="overflow-hidden fixed bottom-2 md:bottom-4 lg:bottom-6 left-0 right-0 z-10">
          <div className="flex gap-3 md:gap-6 animate-slide w-max px-4">
            {[...foot_image, ...foot_image].map((img, index) => (
              <div
                key={index}
                className={`flex-shrink-0 transition-all duration-500 hover:scale-110 hover:z-10 relative cursor-pointer rounded-xl overflow-hidden shadow-lg ${index % foot_image.length === activeImageIndex
                  ? "ring-4 ring-[#9602AD] scale-105 z-10 shadow-[0_0_15px_rgba(150,2,173,0.7)]"
                  : "opacity-90"
                  }`}
                onClick={() => handleImageClick(index)}
              >
                {/* Skeleton loader */}
                {!footerImagesLoaded[index % foot_image.length] && (
                  <div className="absolute inset-0 bg-gradient-to-r from-[#9602AD]/20 to-[#F69E26]/20 animate-pulse rounded-lg"></div>
                )}

                <NextImage
                  src={img || "/placeholder.svg"}
                  alt={`slide-${index}`}
                  width={300}
                  height={150}
                  className={`rounded-lg object-cover w-[200px] h-[100px] md:w-[300px] md:h-[150px] transition-opacity duration-500 ${footerImagesLoaded[index % foot_image.length] ? "opacity-100" : "opacity-0"
                    }`}
                  placeholder="blur"
                  blurDataURL={getBlurDataURL(index % foot_image.length)}
                  onLoadingComplete={() => {
                    const newLoaded = [...footerImagesLoaded]
                    newLoaded[index % foot_image.length] = true
                    setFooterImagesLoaded(newLoaded)
                  }}
                />

                {index % foot_image.length === activeImageIndex && (
                  <div className="absolute inset-0 border-4 border-[#9602AD] rounded-lg"></div>
                )}
              </div>
            ))}
          </div>
        </article>

        {SponsorDetail && (
          <SponsorDetails
            isSponsorDetails={SponsorDetail}
            setSponsorDetails={setSponsorDetails}
            heading="Sponsorship Details"
          />
        )}

        {ParticipantDetail && (
          <ParticipantDetails
            isParticipantDetails={ParticipantDetail}
            setParticipantDetails={setParticipantDetails}
            heading="Participant Details"
            referral_code={referral_code}
          />
        )}

        {ScratchCardPartner && (
          <ScratchCardPartnerDet
            isScratchCardPartnerDet={ScratchCardPartner}
            setScratchCardPartnerDet={setScratchCardPartner}
            heading="Scratch Card Partner Details"
          />
        )}

        <EnlargedImageModal
          isOpen={isEnlargedModalOpen}
          onClose={() => setIsEnlargedModalOpen(false)}
          imageUrl={selectedEnlargedImage}
        />
      </main>
    </>
  )
}

export default Page
