'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/core';
import { <PERSON><PERSON><PERSON>, Blendbg } from '@/components/icons';
import MainHeader from '@/components/layout/MainHeader';
import Image from 'next/image';
import React, { useState } from 'react'
import ParticipantDetails from '../misc/modals/ParticipantDetails';
import ScratchCardPartnerDet from '../misc/modals/ScratchCardPatner';
import SponsorDetails from '../misc/modals/SponsorDetails';




const Page = () => {

  const [SponsorDetail, setSponsorDetails] = useState(false)
  const [ParticipantDetail, setParticipantDetails] = useState(true)
  const [ScratchCardPartner, setScratchCardPartner] = useState(false)

  return (
    <main className="bg-[url('/images/bgImage.png')] min-h-screen bg-no-repeat text-white [background-size:cover] pt-5 md:pt-14 ">
      <section className='px-5 md:px-10 xl:px-32 '>
        <MainHeader />
        <div className="my-10 -mt-5 grid items-center lg:-mt-10 lg:grid-cols-2">
          <div className="relative mx-auto max-w-xl lg:ml-0 lg:mt-10 xl:mt-0 xl:pl-16">
            <div className="relative z-10 text-left lg:pt-4">
              <Blendbg />
              <div className="mr-52 ml-auto mt-12 h-2 w-2 rounded-full bg-[#1CA78B] opacity-50"></div>
              <div
                className="font-clash font-bold text-white md:text-5xl xl:text-6xl"
              >
                <h2 className='text-5xl md:text-[82px] lg:text-6xl xl:text-[82px]'> Salary 4 Life</h2>
                <h2 className='text-5xl md:text-[82px] lg:text-6xl xl:text-[82px] mt-3'><span className='text-[#9602AD]'>Game </span> Show</h2>
              </div>
              <p className="my-2 md:my-4 max-w-lg text-[#c0c0c0] text-sm md:text-lg xl:text-base">
                Be a part of the <span className='text-[#9602AD] font-bold'>Salary 4 Life Game Show</span> coming soon. Be part of our auditions in June, Lagos for a chance to be our next salary4life winner of 100m
              </p>
              <div className="md:my-3">
                <div className="mr-36 ml-auto h-3 w-3 rounded-full bg-[#1CA78B] opacity-50"></div>
              </div>
            </div>
            {/* <div className='grid grid-cols-2 md:flex gap-2 mt-1 md:mt-[2.625rem]'> */}
            {/* <Button
                className='bg-gradient-to-r from-[#4C1961] to-[#9602AD] text-white py-3 md:px-14 lg:px-10 xl:px-10 text-sm text-nowrap'
                type='button'
                onClick={() => setSponsorDetails(true)}
              >
                Sponsor show
              </Button> */}
            <Button
              className='text-white bg-gradient-to-r w-full md:w-fit from-[#F69E26] to-[#9602AD] top-0 text-s0 md:text-lg py-3 md:px-20'
              type='button'
              onClick={() => setParticipantDetails(true)}
            >
              Participate Now
            </Button>
            {/* <Button
                className='text-[#141414] text-xs md:text-sm bg-white py-3 px-3 md:text-nowrap hidden md:flex'
                type='button'
                onClick={() => setScratchCardPartner(true)}
              >
                Scratch Card Partnership
              </Button> */}
            {/* </div> */}
            <div className='mt-4 flex md:hidden'>
              {/* <Button
                className='text-[#141414] bg-white py-3 text-nowrap text-sm w-full'
                type='button'
                onClick={() => setScratchCardPartner(true)}
              >
                Scratch Card Partnership
              </Button> */}
            </div>
          </div>

          <div className="relative mx-auto mt-6 xl:mt-16">
            <div className="lg:block">
              <Image
                className="relative z-10 object-contain"
                src="/images/S4LYF.png"
                width={691}
                height={600}
                alt="Male wearing hat excited and touching his hat"
              />
            </div>
          </div>
        </div>
      </section>

      <article className='bg-[#421651fa] md:bg-[#42165153] py-2 md:py-14 bottom-0 fixed w-full z-20 md:mt-10 lg:mt-0'>
        <div className='md:bg-[#FFFFFF20] py-1 md:py-7 px-5 md:px-10 xl:px-16 flex justify-between items-center'>
          <p className='hidden xl:flex'>All rights reserved. &copy; Whisper Konnect LTD</p>
          <div className='flex flex-col md:flex-row md:gap-24 md:items-center mb-3 md:mb-0'>
            <p className='font-medium'>Win
              <span className='font-bold'> ₦10million </span>
              weekly to power your business needs
            </p>
            {/* <LinkButton
              href={'https://whisper-wyze-ticket.vercel.app/'}
              className='flex items-center bg-gradient-to-r from-[#F69E26]  text-nowrap to-[#9602AD] gap-2 xl:gap-4 text-sm font-medium py-3 w-fit mt-3 md:mt-0'>
              <p className=''>Buy Ticket Now</p>
              <ArrowIcon />
            </LinkButton> */}

          </div>
        </div>
      </article>

      {SponsorDetail &&
        <SponsorDetails
          isSponsorDetails={SponsorDetail}
          setSponsorDetails={setSponsorDetails}
          heading='Sponsorship Details'
        />
      }

      {ParticipantDetail &&
        <ParticipantDetails
          isParticipantDetails={ParticipantDetail}
          setParticipantDetails={setParticipantDetails}
          heading='Participant Details'
          closeLink='PARTICIPANT'
        />
      }

      {ScratchCardPartner &&
        <ScratchCardPartnerDet
          isScratchCardPartnerDet={ScratchCardPartner}
          setScratchCardPartnerDet={setScratchCardPartner}
          heading='Scratch Card Partner Details'
        />
      }

    </main>
  )
}

export default Page