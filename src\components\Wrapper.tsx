'use client'

import * as React from 'react';

import { checkIsIOS, disableIOSTextFieldZoom } from '@/utils/inputs';


export function Wrapper({
  children,
}: {
  children: React.ReactNode;
}) {

  React.useEffect(() => {
    // Check if the current device is iOS and disable text field zooming.
    if (checkIsIOS()) {
      disableIOSTextFieldZoom();
    }
  }, []);

  return (
    <>
      <React.Suspense>
        {children}
      </React.Suspense>
    </>
  );
}
