import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogClose } from '.';
import React, { FC, ReactNode } from 'react';
import { cn } from '@/utils/classNames';
import { TrashIcon } from '@radix-ui/react-icons';
import { SmallSpinner } from '../icons';
// import { SmallSpinner, TrashIcon } from '@/icons/core';

interface Props {
    isModalOpen: boolean;
    closeModal: () => void
    deleteFunction: () => void
    children: ReactNode
    title: string;
    isDeleting?: boolean
}

const ConfirmDeleteModal: FC<Props> = ({ isModalOpen, deleteFunction, closeModal, children, title, isDeleting }) => {

    return (
        <Dialog modal={true} open={isModalOpen} >

            <DialogContent
                aria-label={title}
                className={cn(
                    'rounded-[10px]',
                    'my-6 '
                )}
                style={{
                    width: '92.5%',
                    minWidth: '300px',
                }}
                onPointerDownOutside={closeModal}
            >
                <DialogHeader className={cn('max-sm:sticky top-0 z-10 bg-danger-darker')}>
                    <h5 className='text-base font-medium '>Confirm Delete</h5>
                    <DialogClose className="ml-auto bg-white text-red-900" onClick={closeModal}>
                        Close
                    </DialogClose>
                </DialogHeader>
                <DialogBody className={cn('')}>

                    <div className='flex flex-col px-2'>
                        <span className='flex items-center justify-center bg-danger-darker h-14 w-14 rounded-full p-3 max-w-max'>
                            <TrashIcon className='w-12 h-12 text-white' color='white' />
                        </span>

                        <h3 className='text-danger-dark text-xl font-medium mt-1'>{title}</h3>
                        {children}
                    </div>
                </DialogBody>


                <DialogFooter className='flex items-center justify-end gap-4 bg-danger-light w-full rounded-2xl p-5'>
                    <Button className='py-2 text-header-text hover:opacity-40 border-[1.5px] border-transparent hover:bg-white hover:border-header-text hover:text-header-text' variant="white" onClick={closeModal}>
                        Cancel
                    </Button>
                    <Button className='flex items-center gap-2 py-2 bg-danger-darker hover:!border-danger-darker  hover:text-white' disabled={isDeleting} onClick={deleteFunction}>
                        Delete
                        {
                            isDeleting && <SmallSpinner color='white' />
                        }
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default ConfirmDeleteModal;