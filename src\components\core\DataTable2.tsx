'use client'

import { type ColumnDef, flexRender, getCoreRowModel, type PaginationState, useReactTable } from "@tanstack/react-table"
import React, { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./Table"
import { cn } from "@/utils/classNames"
import Spinner from "../icons/Spinner"

interface DataTable2Props<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  highlightRow?: boolean
  highlightRowById?: string
  isFetching?: boolean
  isLoading?: boolean
  pageCount: number
  pageIndex: number
  pageSize: number
  rows: TData[] | null | undefined
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>
  hasOuterPadding?: boolean
  tableContainerClassName?: string
  setSelectedRows?: React.Dispatch<React.SetStateAction<TData[] | undefined>>
  setRestockPayload?: (submission: TData[]) => void
  selectedRows?: TData[]
  hasCheckBox?: boolean
  restockPayload?: TData[]
  emptyRestockCountRef?: { current: number }
  cursorRow?: string
}

export function DataTable2<TData, TValue>({
  columns,
  highlightRow = false,
  highlightRowById = "",
  isFetching,
  isLoading,
  pageCount,
  pageIndex,
  pageSize,
  rows,
  setPagination,
  hasOuterPadding = true,
  tableContainerClassName,
  setSelectedRows,
  setRestockPayload,
  hasCheckBox,
  restockPayload,
  emptyRestockCountRef,
  cursorRow = "pointer",
}: DataTable2Props<TData, TValue>) {
  const defaultData = React.useMemo(() => [], [])
  const [selectedPageSize, setSelectedPageSize] = useState(pageSize)
  const pageSizeArray = [10, 20, 30, 40, 50]

  const pagination = React.useMemo(
    () => ({
      pageIndex,
      pageSize: selectedPageSize,
    }),
    [pageIndex, selectedPageSize],
  )

  const table = useReactTable({
    data: rows ?? defaultData,
    columns,
    pageCount: pageCount ?? -1,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
  })

  const [selectedRowId, setSelectedRowId] = React.useState("")

  const handleRowClick = (rowId: string) => {
    setSelectedRowId(rowId)
  }

  const selectedItems = table.getSelectedRowModel().rows.map((item) => {
    return item.original
  })

  React.useEffect(() => {
    if (hasCheckBox) {
      setSelectedRows?.(selectedItems)
      setRestockPayload?.(selectedItems)
    }
    if (restockPayload?.length === 0 && emptyRestockCountRef && emptyRestockCountRef?.current >= 2) {
      table.toggleAllRowsSelected(false)
      emptyRestockCountRef.current = 1
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasCheckBox, setSelectedRows, setRestockPayload, restockPayload?.length, emptyRestockCountRef])

  return (
    <>
      <div
        className={cn(
          "print:hidden overflow-hidden rounded-full opacity-0 transition-opacity",
          isFetching && !isLoading && "opacity-100",
        )}
      >
        <div className="bg-primary/20 h-1 w-full overflow-hidden">
          <div className="h-full w-full origin-[0_50%] animate-indeterminate-progress rounded-full bg-primary"></div>
        </div>
      </div>

      <div
        className={cn(
          "overflow-auto print:w-full rounded-md bg-white",
          hasOuterPadding && "p-3 md:p-6 md:pt-0 lg:pb-8",
          tableContainerClassName,
        )}
      >
        <Table className="border rounded-xl border-[#E9E9E9]">
          <TableHeader className="bg-[#15171D] rounded-t-xl text-xs">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow className="border-b" key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead className="border-r last:border-r-0 font-semibold text-white px-4 py-3" key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => {
                const idValue = highlightRowById ? row.getValue("id") : undefined

                return (
                  <TableRow
                    className={cn(
                      "border-b last:border-b-0",
                      (highlightRow && row.id === selectedRowId) || highlightRowById === idValue ? "bg-blue-50" : "",
                      cursorRow === "pointer" && "cursor-pointer",
                    )}
                    data-state={row.getIsSelected() && "selected"}
                    key={row.id}
                    onClick={() => handleRowClick(row.id)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell className="border-r last:border-r-0 px-4 py-3" key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                )
              })
            ) : (
              <TableRow>
                <TableCell className="h-24 text-center" colSpan={columns.length}>
                  {isLoading ? <Spinner className="mx-auto" /> : "No results."}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        {/* {(rows?.length || 0) > 0 && (
          <div className="pagination-box my-4 w-full">
            <div className="no-wrap flex w-full flex-row items-center justify-between gap-2 overflow-x-auto">
              <div className="go-to-page flex flex-row">
                <Listbox
                  as="div"
                  className="relative"
                  value={selectedPageSize}
                  onChange={(value: number) => {
                    setSelectedPageSize(value)
                    table.setPageSize(Number(value))
                  }}
                >
                  <Listbox.Button className="flex items-center gap-5 rounded bg-wise-green-bg bg-opacity-20 p-2 text-xs text-white md:px-4 md:text-sm">
                    <span className="font-semibold">{selectedPageSize}</span>{" "}
                    <span>
                      <svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M8.35694 5.48777C7.09859 6.85158 5.08445 6.89704 3.77571 5.62415L3.6429 5.48777L0.410664 1.7672C0.0852261 1.41449 0.0852261 0.842628 0.410664 0.489918C0.711066 0.164338 1.18376 0.139294 1.51067 0.414784L1.58917 0.489918L4.82141 4.21049C5.43802 4.87878 6.41759 4.91395 7.07242 4.31601L7.17843 4.21049L10.4107 0.489918C10.7361 0.137207 11.2637 0.137207 11.5892 0.489918C11.8896 0.815497 11.9127 1.32781 11.6585 1.68211L11.5892 1.7672L8.35694 5.48777Z"
                          fill="#00EB6F"
                        />
                      </svg>
                    </span>
                  </Listbox.Button>

                  <Listbox.Options className="text-liberty-teal absolute z-10 mt-1 w-full rounded bg-[#025A34] text-xs text-opacity-90 md:text-sm">
                    {pageSizeArray.map((pageSize) => (
                      <Listbox.Option
                        key={pageSize}
                        value={pageSize}
                        className="cursor-pointer rounded py-2 px-4 hover:bg-wise-green-bg hover:bg-opacity-40"
                      >
                        {pageSize}
                      </Listbox.Option>
                    ))}
                  </Listbox.Options>
                </Listbox>

                <span className="flex items-center gap-1">
                  <span className="ml-4 text-[10px] text-[#848484] lg:text-sm">
                    Showing {pageIndex == 0 ? pageIndex + 1 : pageIndex * selectedPageSize + 1}
                    &nbsp;to&nbsp;
                    {(pageIndex + 1) * selectedPageSize}
                    &nbsp;out of&nbsp;
                    {rows?.length} entries
                  </span>
                </span>
              </div>

              <div className="flex">
                <button
                  className="mx-1 inline-block h-10 w-10 rounded border transition duration-500 ease-in-out hover:border-none hover:bg-hoverPurple"
                  onClick={() => table.setPageIndex(0)}
                  disabled={!table.getCanPreviousPage()}
                >
                  {"<<"}
                </button>
                <button
                  className="mx-1 h-10 w-10 rounded border transition duration-500 ease-in-out hover:border-none hover:bg-hoverPurple"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  {"<"}
                </button>
                <button
                  className="mx-1 h-10 w-10 rounded border transition duration-500 ease-in-out hover:border-none hover:bg-hoverPurple"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  {">"}
                </button>
                <button
                  className="mx-1 h-10 w-10 rounded border transition duration-500 ease-in-out hover:border-none hover:bg-hoverPurple"
                  onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                  disabled={!table.getCanNextPage()}
                >
                  {">>"}
                </button>
              </div>
            </div>
          </div>
        )} */}
      </div>
    </>
  )
}

