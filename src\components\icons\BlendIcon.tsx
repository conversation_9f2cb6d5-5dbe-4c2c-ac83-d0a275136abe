import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    className="absolute -top-10 right-64"
    width={50}
    height={48}
    viewBox="0 0 50 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g filter="url(#filter0_f_4798_21126)">
      <rect x={20} y={20} width={9.86667} height={8} fill="#CE00EF" />
    </g>
    <rect
      x={20.709}
      y={20.3545}
      width={7.4452}
      height={7.4452}
      fill="#4C1961"
    />
    <defs>
      <filter
        id="filter0_f_4798_21126"
        x={0}
        y={0}
        width={49.8672}
        height={48}
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feGaussianBlur
          stdDeviation={10}
          result="effect1_foregroundBlur_4798_21126"
        />
      </filter>
    </defs>
  </svg>
);
export default SVGComponent;
