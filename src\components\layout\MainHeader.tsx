'use client'

import React, { useState } from 'react'
import { ArrowIcon, Menubar, Salary4Lyf } from '../icons'
import Link from 'next/link'
import { Button, LinkButton } from '../core'
import { cn } from '@/utils/classNames'
import { usePathname, useSearchParams } from 'next/navigation'
import MenuModal from './MobileHeader'
import SponsorDetails from '@/app/misc/modals/SponsorDetails'
import ParticipantDetails from '@/app/misc/modals/ParticipantDetails'

const MainHeader = () => {
    const params = useSearchParams();

    const referral_code = params.get('referral') || '';
  
    console.log(referral_code, "referral header")

    
    const [isMobileMenuModal, setMobileMenuModal] = useState(false)
    const [SponsorDetail, setSponsorDetails] = useState(false)
    const [ParticipantDetail, setParticipantDetails] = useState(false)

    const pathname = usePathname()

    const headerLink = [
        {
            link: '/',
            name: 'Home',
        },
        {
            link: '#',
            name: 'About Us',
        },
        {
            link: '#',
            name: 'FAQs',
        },
        {
            link: '/contact-us',
            name: 'Contact Us',
        },
    ]

    return (
        <header>
            <div className='flex justify-between items-center xl:pl-16'>
                <LinkButton href='/' className='!px-0'>
                    <Salary4Lyf />
                </LinkButton>

                {/* <nav className='md:flex gap-5 xl:gap-7 hidden'>
                    {
                        headerLink.map((href, index) => (
                            <div key={index}>
                                <Link
                                    href={href?.link}
                                    className={cn("hover:text-[#9C9C9C] text-sm",
                                        pathname == href?.link ? "text-white font-semibold" : "text-[#9C9C9C]"
                                    )}
                                >{href?.name}</Link>
                            </div>
                        ))
                    }
                </nav> */}
                {/* <div className='flex md:hidden'>
                    <Button
                    onClick={() => { setMobileMenuModal(true) }}
                    >
                        <Menubar />
                    </Button>
                </div> */}
                {/* <Button
                    className='hidden md:flex bg-gradient-to-r from-[#4C1961] to-[#9602AD] xl:px-14 py-3 xl:text-lg font-medium'
                    type='button'
                    onClick={() => setSponsorDetails(true)}
                >
                    Sponsor show
                </Button> */}
                <div className='flex items-center gap-1.5 md:gap-4'>
                    <LinkButton href='/contact-us' className='bg-gradient-to-r from-[#4C1961] to-[#9602AD] text-xs px-2 md:px-3 xl:px-14 py-3 xl:text-lg font-medium text-nowrap '>Contact Us</LinkButton>
                    {/* <LinkButton
                        href={'https://whisper-wyze-ticket.vercel.app/'}
                        className='flex items-center bg-gradient-to-r from-[#F69E26] text-nowrap to-[#9602AD] px-2 gap-2 xl:px-6 py-1.5 xl:py-3 xl:text-lg font-medium '>
                        <p className=''>Buy Ticket Now</p>
                        <ArrowIcon />
                    </LinkButton> */}
                    <Button
                        className="text-white whitespace-nowrap bg-gradient-to-r w-full md:w-fit from-[#F69E26] to-[#9602AD] hidden sm:block top-0 text-sm md:text-lg py-3 md:px-20 relative group hover:scale-105 transition-all duration-300 animate-float shadow-[0_0_15px_rgba(150,2,173,0.3)] hover:shadow-[0_0_25px_rgba(150,2,173,0.5)]"
                        type="button"
                        onClick={() => setParticipantDetails(true)}
                    >
                        <span className="relative z-10">Participate Now</span>
                        <div className="absolute inset-0 bg-gradient-to-r from-[#F69E26] to-[#9602AD] rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"></div>
                        <div className="absolute -inset-1 bg-gradient-to-r from-[#F69E26] to-[#9602AD] rounded-md opacity-0 group-hover:opacity-20 transition-opacity duration-300 animate-pulse"></div>
                    </Button>
                </div>
            </div>

            {
                isMobileMenuModal &&
                <MenuModal
                    isMenuModal={isMobileMenuModal}
                    setMenuModal={setMobileMenuModal}
                    heading={''}
                />
            }
            {SponsorDetail &&
                <SponsorDetails
                    isSponsorDetails={SponsorDetail}
                    setSponsorDetails={setSponsorDetails}
                    heading='Sponsorship Details'
                />
            }

            {ParticipantDetail && (
                <ParticipantDetails
                    isParticipantDetails={ParticipantDetail}
                    setParticipantDetails={setParticipantDetails}
                    heading="Participant Details"
                    referral_code={referral_code}
                />
            )}

        </header>
    )
}

export default MainHeader