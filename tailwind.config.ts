import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      fontFamily: {
        sans: ['var(--font-sans)'],
        heading: ['var(--font-heading)'],
        'wix-display': ['var(--font-wix-display)'],
        clash: ['var(--font-clash)'],
      },
      fontSize: {
        xxs: '.625rem'
      },
      keyframes: {
        slide: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-50%)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        }
      },
      animation: {
        slide: 'slide 30s linear infinite',
        'float': 'float 3s ease-in-out infinite',
      },
    },
    maxHeight: {
      "modal-content": "max(calc(75vh + 5rem), calc(75svh + 5rem))",
      "modal-body": "max(calc(75vh), calc(75svh))",
    },
  },
  plugins: [],
};
export default config;
